<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI智能PPT制作流程</title>
    <link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-100-M/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap">
    <style>
        :root {
            /* 基础色板 */
            --apple-white: #ffffff;
            --apple-background: #f5f5f7;
            --apple-text-primary: #1d1d1f;
            --apple-text-secondary: #86868b;
            --apple-border: #d2d2d7;
            
            /* 品牌色（仅用于点缀） */
            --apple-blue: rgba(0, 122, 255, 0.25);
            --apple-green: rgba(48, 209, 88, 0.25);
            --apple-orange: rgba(255, 149, 0, 0.25);
            --apple-purple: rgba(175, 82, 222, 0.25);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, "SF Pro Display", "Inter", "PingFang SC", sans-serif;
            background-color: var(--apple-background);
            color: var(--apple-text-primary);
            line-height: 1.6;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
            display: grid;
            grid-template-columns: repeat(12, 1fr);
            grid-template-rows: auto;
            gap: 20px;
            grid-template-areas:
                "hero hero hero hero hero hero hero hero hero hero hero hero"
                "step1 step1 step1 step1 step2 step2 step2 step2 step3 step3 step3 step3"
                "comparison comparison comparison comparison comparison comparison advantage advantage advantage advantage advantage advantage"
                "summary summary summary summary summary summary gamma gamma gamma gamma gamma gamma"
                "stats stats stats stats stats workflow workflow workflow workflow workflow workflow workflow";
        }

        .card {
            background: var(--apple-white);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.08);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 24px rgba(0,0,0,0.12);
        }

        .gradient-orb {
            position: absolute;
            width: 400px;
            height: 400px;
            border-radius: 50%;
            filter: blur(100px);
            opacity: 0.4;
            z-index: 0;
        }

        .gradient-orb.blue {
            background: radial-gradient(circle, var(--apple-blue), transparent);
            top: -200px;
            right: -200px;
        }

        .gradient-orb.green {
            background: radial-gradient(circle, var(--apple-green), transparent);
            bottom: -200px;
            left: -200px;
        }

        .gradient-orb.orange {
            background: radial-gradient(circle, var(--apple-orange), transparent);
            top: -150px;
            left: -150px;
        }

        .gradient-orb.purple {
            background: radial-gradient(circle, var(--apple-purple), transparent);
            bottom: -150px;
            right: -150px;
        }

        .card-content {
            position: relative;
            z-index: 2;
        }

        /* 英雄卡片 */
        .hero-card {
            grid-area: hero;
            text-align: center;
            padding: 60px 40px;
            position: relative;
        }

        .hero-label {
            font-size: 17px;
            font-weight: 500;
            color: var(--apple-text-secondary);
            margin-bottom: 10px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .hero-number {
            font-size: 120px;
            font-weight: 700;
            color: var(--apple-text-primary);
            line-height: 0.9;
            margin: 20px 0;
        }

        .hero-title {
            font-size: 48px;
            font-weight: 600;
            color: var(--apple-text-primary);
            margin-bottom: 20px;
        }

        .hero-subtitle {
            font-size: 24px;
            font-weight: 400;
            color: var(--apple-text-secondary);
            max-width: 600px;
            margin: 0 auto;
        }

        /* 步骤卡片 */
        .step-card {
            text-align: center;
            position: relative;
        }

        .step-card.step1 { grid-area: step1; }
        .step-card.step2 { grid-area: step2; }
        .step-card.step3 { grid-area: step3; }

        .step-icon {
            font-size: 48px;
            color: var(--apple-text-primary);
            margin-bottom: 24px;
            opacity: 0.8;
        }

        .step-number {
            font-size: 72px;
            font-weight: 700;
            color: var(--apple-text-primary);
            line-height: 0.8;
            margin-bottom: 16px;
        }

        .step-title {
            font-size: 24px;
            font-weight: 600;
            color: var(--apple-text-primary);
            margin-bottom: 12px;
        }

        .step-desc {
            font-size: 17px;
            color: var(--apple-text-secondary);
            line-height: 1.5;
        }

        /* 对比卡片 */
        .comparison-card {
            grid-area: comparison;
            position: relative;
            min-height: 300px;
        }

        .comparison-title {
            font-size: 32px;
            font-weight: 600;
            color: var(--apple-text-primary);
            margin-bottom: 30px;
            text-align: center;
        }

        .vs-container {
            display: grid;
            grid-template-columns: 1fr auto 1fr;
            gap: 30px;
            align-items: center;
        }

        .ai-option {
            text-align: center;
            padding: 20px;
        }

        .ai-name {
            font-size: 28px;
            font-weight: 600;
            color: var(--apple-text-primary);
            margin-bottom: 12px;
        }

        .ai-feature {
            font-size: 17px;
            color: var(--apple-text-secondary);
            margin-bottom: 8px;
        }

        .vs-divider {
            width: 2px;
            height: 80px;
            background: var(--apple-border);
            border-radius: 1px;
        }

        /* 优势卡片 */
        .advantage-card {
            grid-area: advantage;
            position: relative;
            min-height: 300px;
        }

        .advantage-title {
            font-size: 28px;
            font-weight: 600;
            color: var(--apple-text-primary);
            margin-bottom: 24px;
        }

        .advantage-item {
            display: flex;
            align-items: center;
            margin-bottom: 16px;
        }

        .advantage-icon {
            font-size: 20px;
            color: var(--apple-text-primary);
            margin-right: 16px;
            opacity: 0.6;
        }

        .advantage-text {
            font-size: 17px;
            color: var(--apple-text-secondary);
        }

        /* Gamma卡片 */
        .gamma-card {
            grid-area: gamma;
            position: relative;
            min-height: 250px;
        }

        .gamma-title {
            font-size: 32px;
            font-weight: 600;
            color: var(--apple-text-primary);
            margin-bottom: 24px;
        }

        .gamma-steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .gamma-step {
            text-align: center;
            padding: 20px;
            border-radius: 16px;
            background: var(--apple-background);
        }

        .gamma-step-number {
            font-size: 24px;
            font-weight: 700;
            color: var(--apple-text-primary);
            margin-bottom: 8px;
        }

        .gamma-step-text {
            font-size: 15px;
            color: var(--apple-text-secondary);
        }

        /* 总结卡片 */
        .summary-card {
            grid-area: summary;
            position: relative;
        }

        .summary-title {
            font-size: 28px;
            font-weight: 600;
            color: var(--apple-text-primary);
            margin-bottom: 24px;
        }

        .summary-text {
            font-size: 17px;
            color: var(--apple-text-secondary);
            line-height: 1.6;
            margin-bottom: 16px;
        }

        /* 统计卡片 */
        .stats-card {
            grid-area: stats;
            position: relative;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 24px;
            text-align: center;
        }

        .stat-number {
            font-size: 44px;
            font-weight: 700;
            color: var(--apple-text-primary);
            line-height: 1;
            margin-bottom: 6px;
        }

        .stat-label {
            font-size: 14px;
            color: var(--apple-text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* 工作流程卡片 */
        .workflow-card {
            grid-area: workflow;
            position: relative;
        }

        .workflow-title {
            font-size: 28px;
            font-weight: 600;
            color: var(--apple-text-primary);
            margin-bottom: 24px;
        }

        .workflow-steps {
            display: grid;
            grid-template-columns: 1fr auto 1fr auto 1fr auto 1fr;
            align-items: center;
            gap: 16px;
        }

        .workflow-step {
            text-align: center;
            padding: 16px 12px;
        }

        .workflow-step-icon {
            font-size: 28px;
            color: var(--apple-text-primary);
            margin-bottom: 8px;
            opacity: 0.7;
        }

        .workflow-step-title {
            font-size: 15px;
            font-weight: 600;
            color: var(--apple-text-primary);
            margin-bottom: 4px;
            line-height: 1.2;
        }

        .workflow-step-time {
            font-size: 13px;
            color: var(--apple-text-secondary);
        }

        .workflow-arrow {
            font-size: 16px;
            color: var(--apple-text-secondary);
            opacity: 0.4;
        }

        /* 背景图标水印 */
        .bg-icon {
            position: absolute;
            font-size: 200px;
            color: var(--apple-text-primary);
            opacity: 0.03;
            z-index: 1;
        }

        .bg-icon.top-right {
            top: 20px;
            right: 20px;
        }

        .bg-icon.bottom-left {
            bottom: 20px;
            left: 20px;
        }

        .bg-icon.center {
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .container {
                grid-template-columns: 1fr;
                grid-template-areas:
                    "hero"
                    "step1"
                    "step2"
                    "step3"
                    "comparison"
                    "advantage"
                    "gamma"
                    "summary"
                    "stats"
                    "workflow";
            }
            
            .hero-number {
                font-size: 80px;
            }
            
            .hero-title {
                font-size: 32px;
            }
            
            .hero-subtitle {
                font-size: 18px;
            }
            
            .card {
                padding: 30px;
            }

            .workflow-steps {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .workflow-arrow {
                transform: rotate(90deg);
                justify-self: center;
            }

            .stats-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .stat-number {
                font-size: 36px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 英雄卡片 -->
        <div class="card hero-card">
            <div class="gradient-orb blue"></div>
            <i class="fas fa-magic bg-icon center"></i>
            <div class="card-content">
                <p class="hero-label">AI智能工作流</p>
                <h1 class="hero-number">30</h1>
                <p class="hero-title">分钟制作专业PPT</p>
                <p class="hero-subtitle">DeepResearch + Gamma = 高效内容创作新范式</p>
            </div>
        </div>

        <!-- 步骤卡片组 -->
        <div class="card step-card step1">
            <div class="gradient-orb orange"></div>
            <i class="fas fa-search bg-icon top-right"></i>
            <div class="card-content">
                <i class="fas fa-brain step-icon"></i>
                <div class="step-number">01</div>
                <h3 class="step-title">选择研究方案</h3>
                <p class="step-desc">Gemini全面覆盖 vs GPT精确深挖，根据需求选择最适合的AI研究助手</p>
            </div>
        </div>

        <div class="card step-card step2">
            <div class="gradient-orb green"></div>
            <i class="fas fa-chart-line bg-icon top-right"></i>
            <div class="card-content">
                <i class="fas fa-balance-scale step-icon"></i>
                <div class="step-number">02</div>
                <h3 class="step-title">对比研究结果</h3>
                <p class="step-desc">边思考边搜索，高质量数据无AI幻觉，确保信息准确性和时效性</p>
            </div>
        </div>

        <div class="card step-card step3">
            <div class="gradient-orb purple"></div>
            <i class="fas fa-presentation bg-icon top-right"></i>
            <div class="card-content">
                <i class="fas fa-wand-magic-sparkles step-icon"></i>
                <div class="step-number">03</div>
                <h3 class="step-title">Gamma制作PPT</h3>
                <p class="step-desc">自动分页、智能排版、AI生成图片，一键产出专业级演示文稿</p>
            </div>
        </div>

        <!-- 对比卡片 */
        <div class="card comparison-card">
            <i class="fas fa-vs bg-icon center"></i>
            <div class="card-content">
                <h2 class="comparison-title">AI研究助手对比</h2>
                <div class="vs-container">
                    <div class="ai-option">
                        <h3 class="ai-name">Gemini</h3>
                        <p class="ai-feature">覆盖面广</p>
                        <p class="ai-feature">行业概览</p>
                        <p class="ai-feature">背景分析</p>
                    </div>
                    <div class="vs-divider"></div>
                    <div class="ai-option">
                        <h3 class="ai-name">GPT</h3>
                        <p class="ai-feature">精确深挖</p>
                        <p class="ai-feature">具体产品</p>
                        <p class="ai-feature">细化问题</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 优势卡片 */
        <div class="card advantage-card">
            <i class="fas fa-rocket bg-icon bottom-left"></i>
            <div class="card-content">
                <h2 class="advantage-title">Deep Research优势</h2>
                <div class="advantage-item">
                    <i class="fas fa-sync-alt advantage-icon"></i>
                    <span class="advantage-text">边思考边搜索新内容</span>
                </div>
                <div class="advantage-item">
                    <i class="fas fa-shield-check advantage-icon"></i>
                    <span class="advantage-text">搜索质量高，数据可信</span>
                </div>
                <div class="advantage-item">
                    <i class="fas fa-ban advantage-icon"></i>
                    <span class="advantage-text">无AI幻觉问题</span>
                </div>
                <div class="advantage-item">
                    <i class="fas fa-clock advantage-icon"></i>
                    <span class="advantage-text">实时信息获取</span>
                </div>
            </div>
        </div>

        <!-- Gamma卡片 */
        <div class="card gamma-card">
            <div class="gradient-orb blue"></div>
            <i class="fas fa-layer-group bg-icon top-right"></i>
            <div class="card-content">
                <h2 class="gamma-title">Gamma智能制作流程</h2>
                <div class="gamma-steps">
                    <div class="gamma-step">
                        <div class="gamma-step-number">粘贴</div>
                        <p class="gamma-step-text">导入研究报告</p>
                    </div>
                    <div class="gamma-step">
                        <div class="gamma-step-number">保留</div>
                        <p class="gamma-step-text">原文自动分页</p>
                    </div>
                    <div class="gamma-step">
                        <div class="gamma-step-number">压缩</div>
                        <p class="gamma-step-text">智能内容优化</p>
                    </div>
                    <div class="gamma-step">
                        <div class="gamma-step-number">生成</div>
                        <p class="gamma-step-text">AI配图排版</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 总结卡片 -->
        <div class="card summary-card">
            <i class="fas fa-check-circle bg-icon bottom-left"></i>
            <div class="card-content">
                <h2 class="summary-title">核心价值</h2>
                <p class="summary-text">十几分钟快速了解行业主题，专业级分析报告自动生成</p>
                <p class="summary-text">可继续深化研究具体方向，获得更专业的细分析</p>
                <p class="summary-text">增强输入信息 + 结构化输出 = 高效工作新范式</p>
            </div>
        </div>

        <!-- 统计卡片 -->
        <div class="card stats-card">
            <div class="card-content">
                <div class="stats-grid">
                    <div>
                        <div class="stat-number">20</div>
                        <p class="stat-label">分钟完成</p>
                    </div>
                    <div>
                        <div class="stat-number">100%</div>
                        <p class="stat-label">自动化</p>
                    </div>
                    <div>
                        <div class="stat-number">10x</div>
                        <p class="stat-label">效率提升</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 工作流程卡片 -->
        <div class="card workflow-card">
            <div class="card-content">
                <h2 class="workflow-title">完整工作流程</h2>
                <div class="workflow-steps">
                    <div class="workflow-step">
                        <i class="fas fa-search workflow-step-icon"></i>
                        <h4 class="workflow-step-title">Deep Research</h4>
                        <p class="workflow-step-time">5-10分钟</p>
                    </div>
                    <i class="fas fa-arrow-right workflow-arrow"></i>
                    <div class="workflow-step">
                        <i class="fas fa-file-alt workflow-step-icon"></i>
                        <h4 class="workflow-step-title">报告整理</h4>
                        <p class="workflow-step-time">2-3分钟</p>
                    </div>
                    <i class="fas fa-arrow-right workflow-arrow"></i>
                    <div class="workflow-step">
                        <i class="fas fa-magic workflow-step-icon"></i>
                        <h4 class="workflow-step-title">Gamma生成</h4>
                        <p class="workflow-step-time">5分钟</p>
                    </div>
                    <i class="fas fa-arrow-right workflow-arrow"></i>
                    <div class="workflow-step">
                        <i class="fas fa-check workflow-step-icon"></i>
                        <h4 class="workflow-step-title">完成交付</h4>
                        <p class="workflow-step-time">即时</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>