<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI游戏发行Agent - 技术概念内部分享</title>
    <link href="https://cdn.staticfile.net/tailwindcss/2.2.9/tailwind.min.css" rel="stylesheet">
    <link href="https://cdn.staticfile.net/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400;500;600;700&family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/mermaid@latest/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Noto Sans SC', <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, "Droid Sans", "Helvetica Neue", "Droid Sans Fallback", "Heiti SC", "Hiragino Sans GB", <PERSON><PERSON>, sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            color: #e2e8f0;
            overflow-x: hidden;
        }
        
        .gradient-text {
            background: linear-gradient(135deg, #60a5fa 0%, #a855f7 50%, #06b6d4 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .hero-bg {
            background: radial-gradient(ellipse at center, rgba(96, 165, 250, 0.1) 0%, transparent 70%),
                        linear-gradient(135deg, #0f0f23 0%, #1a1a2e 100%);
        }
        
        .card-hover {
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            background: rgba(30, 41, 59, 0.3);
            border: 1px solid rgba(96, 165, 250, 0.2);
        }
        
        .card-hover:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(96, 165, 250, 0.3);
            border-color: rgba(96, 165, 250, 0.5);
        }
        
        .pulse-ring {
            animation: pulse-ring 2s infinite;
        }
        
        @keyframes pulse-ring {
            0% { box-shadow: 0 0 0 0 rgba(96, 165, 250, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(96, 165, 250, 0); }
            100% { box-shadow: 0 0 0 0 rgba(96, 165, 250, 0); }
        }
        
        .floating {
            animation: floating 3s ease-in-out infinite;
        }
        
        @keyframes floating {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
        
        .tech-grid {
            background-image: 
                linear-gradient(rgba(96, 165, 250, 0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(96, 165, 250, 0.1) 1px, transparent 1px);
            background-size: 50px 50px;
        }
        
        .bento-grid {
            display: grid;
            grid-template-columns: repeat(12, 1fr);
            grid-template-rows: repeat(6, 1fr);
            gap: 1rem;
            height: 100vh;
            padding: 2rem;
        }
        
        .bento-item {
            border-radius: 1rem;
            padding: 1.5rem;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(96, 165, 250, 0.2);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            transition: all 0.3s ease;
            overflow: hidden;
            position: relative;
        }
        
        .bento-item:hover {
            border-color: rgba(96, 165, 250, 0.5);
            transform: scale(1.02);
        }
        
        .bento-1 { grid-column: 1 / 5; grid-row: 1 / 4; background: linear-gradient(135deg, rgba(96, 165, 250, 0.2), rgba(168, 85, 247, 0.2)); }
        .bento-2 { grid-column: 5 / 9; grid-row: 1 / 3; background: linear-gradient(135deg, rgba(6, 182, 212, 0.2), rgba(34, 197, 94, 0.2)); }
        .bento-3 { grid-column: 9 / 13; grid-row: 1 / 4; background: linear-gradient(135deg, rgba(251, 146, 60, 0.2), rgba(239, 68, 68, 0.2)); }
        .bento-4 { grid-column: 1 / 4; grid-row: 4 / 7; background: linear-gradient(135deg, rgba(168, 85, 247, 0.2), rgba(219, 39, 119, 0.2)); }
        .bento-5 { grid-column: 4 / 10; grid-row: 3 / 6; background: linear-gradient(135deg, rgba(34, 197, 94, 0.2), rgba(6, 182, 212, 0.2)); }
        .bento-6 { grid-column: 10 / 13; grid-row: 4 / 7; background: linear-gradient(135deg, rgba(239, 68, 68, 0.2), rgba(251, 146, 60, 0.2)); }
        .bento-7 { grid-column: 4 / 8; grid-row: 6 / 7; background: linear-gradient(135deg, rgba(96, 165, 250, 0.2), rgba(6, 182, 212, 0.2)); }
        .bento-8 { grid-column: 8 / 10; grid-row: 6 / 7; background: linear-gradient(135deg, rgba(168, 85, 247, 0.2), rgba(96, 165, 250, 0.2)); }
        
        .timeline-item {
            position: relative;
            padding-left: 3rem;
            margin-bottom: 2rem;
        }
        
        .timeline-item::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0.5rem;
            width: 12px;
            height: 12px;
            background: linear-gradient(135deg, #60a5fa, #a855f7);
            border-radius: 50%;
        }
        
        .timeline-item::after {
            content: '';
            position: absolute;
            left: 5px;
            top: 1.5rem;
            width: 2px;
            height: calc(100% + 1rem);
            background: linear-gradient(to bottom, rgba(96, 165, 250, 0.5), transparent);
        }
        
        .progress-bar {
            height: 8px;
            background: rgba(96, 165, 250, 0.2);
            border-radius: 4px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #60a5fa, #a855f7);
            border-radius: 4px;
            animation: progress-fill 2s ease-out;
        }
        
        @keyframes progress-fill {
            from { width: 0%; }
        }

        .scroll-indicator {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: rgba(96, 165, 250, 0.2);
            z-index: 1000;
        }

        .scroll-progress {
            height: 100%;
            background: linear-gradient(90deg, #60a5fa, #a855f7);
            width: 0%;
            transition: width 0.1s ease;
        }

        .nav-blur {
            backdrop-filter: blur(20px);
            background: rgba(15, 15, 35, 0.8);
            border-bottom: 1px solid rgba(96, 165, 250, 0.2);
        }

        .mermaid {
            display: flex !important;
            justify-content: center !important;
            align-items: center !important;
            width: 100% !important;
        }

        .mermaid svg {
            max-width: 100% !important;
            height: auto !important;
        }
    </style>
</head>
<body>
    <!-- 滚动进度条 -->
    <div class="scroll-indicator">
        <div class="scroll-progress" id="scrollProgress"></div>
    </div>

    <!-- 导航栏 -->
    <nav class="nav-blur fixed top-0 w-full z-50 transition-all duration-300">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center space-x-2">
                    <i class="fas fa-robot text-2xl gradient-text"></i>
                    <span class="text-xl font-bold text-white">AI游戏发行Agent</span>
                    <span class="text-xs text-gray-400 ml-2">技术分享</span>
                </div>
                <div class="hidden md:flex space-x-8">
                    <a href="#hero" class="hover:text-blue-400 transition-colors">首页</a>
                    <a href="#architecture" class="hover:text-blue-400 transition-colors">架构</a>
                    <a href="#features" class="hover:text-blue-400 transition-colors">功能</a>
                    <a href="#vision" class="hover:text-blue-400 transition-colors">愿景</a>
                    <a href="#summary" class="hover:text-blue-400 transition-colors">总结</a>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="hero" class="hero-bg min-h-screen flex items-center justify-center relative overflow-hidden tech-grid">
        <div class="absolute inset-0 bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-cyan-500/10"></div>
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10">
            <div class="floating">
                <i class="fas fa-gamepad text-6xl md:text-8xl gradient-text mb-8 pulse-ring"></i>
            </div>
            <h1 class="text-4xl md:text-7xl font-bold mb-6">
                <span class="gradient-text">AI游戏发行Agent</span>
            </h1>
            <p class="text-xl md:text-2xl text-gray-300 mb-8 max-w-4xl mx-auto leading-relaxed">
                基于AI技术的游戏发行解决方案<br>
                <span class="text-green-400 font-semibold">"更智能的投放，更精准的运营，更高效的转化"</span>
            </p>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12 max-w-4xl mx-auto">
                <div class="card-hover rounded-xl p-6">
                    <div class="text-3xl font-bold text-green-400">3倍</div>
                    <div class="text-sm text-gray-400">投放效率提升</div>
                </div>
                <div class="card-hover rounded-xl p-6">
                    <div class="text-3xl font-bold text-blue-400">25%</div>
                    <div class="text-sm text-gray-400">营销成本降低</div>
                </div>
                <div class="card-hover rounded-xl p-6">
                    <div class="text-3xl font-bold text-purple-400">30%+</div>
                    <div class="text-sm text-gray-400">整体ROI提升</div>
                </div>
            </div>
            <div class="mb-12 text-lg text-gray-300">
                <p class="mb-4">
                    <span class="text-cyan-400 font-semibold">2025年AI游戏元年</span>已至，全球AI在游戏市场规模将<span class="text-yellow-400 font-semibold">巨增2000亿</span>
                </p>
                <p>腾讯、网易、米哈游等头部厂商正全力布局，<span class="text-green-400 font-semibold">现在是抢占制高点的最佳时机</span></p>
            </div>
            <div class="text-center">
                <div class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-500/20 to-purple-600/20 rounded-full border border-blue-400/30">
                    <i class="fas fa-lightbulb text-yellow-400 mr-2"></i>
                    <span class="text-sm text-gray-300">技术概念展示 - 内部分享版本</span>
                </div>
            </div>
        </div>
    </section>

    <!-- 系统架构说明 -->
    <section id="architecture" class="py-20 relative">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-4xl md:text-5xl font-bold mb-6">
                    <span class="gradient-text">游戏发行 Agent 系统架构</span>
                </h2>
                <p class="text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed">
                    发行 Agent 系统由<span class="text-blue-400 font-semibold">投放 Agent</span> 和 <span class="text-purple-400 font-semibold">运营 Agent</span> 两个核心 Agent 组成，
                    通过<span class="text-cyan-400 font-semibold">中枢大模型</span>进行统一调度和协调，实现从获客到留存的全流程智能化
                </p>
            </div>

            <!-- 系统架构图 -->
            <div class="mb-16">
                <div class="bg-slate-800/50 rounded-2xl p-8 backdrop-filter backdrop-blur-lg border border-blue-500/20 flex justify-center">
                    <div class="mermaid" id="architectureDiagram">
graph TB
    subgraph "🧠 中枢大模型调度系统"
        C1[统一调度中心] --> C2[任务分配引擎]
        C2 --> C3[协调决策模块]
        C3 --> C4[数据融合分析]
    end

    subgraph "🎯 投放 Agent"
        A1[热门素材爬虫] --> A2[素材工厂]
        A2 --> A3[创意实验室]
        A3 --> A4[多平台投放]
        A4 --> A5[归因分析]
        A5 --> A6[自动调量]
        A6 --> A7[ROI-Oracle回本预测]
    end

    subgraph "👥 运营 Agent"
        B1[用户洞察] --> B2[活动设计器]
        B2 --> B3[流失预警++]
        B3 --> B4[客服机器人]
        B4 --> B5[专业化工具集]
    end

    C4 --> A1
    C4 --> B1
    A7 --> C1
    B5 --> C1

    C1 --> D1[📈 投放效率提升3倍]
    C1 --> D2[📈 营销成本降低25%]
    C1 --> D3[📈 整体ROI提升30%+]

    style C1 fill:#06b6d4,stroke:#0891b2,color:#fff
    style C2 fill:#06b6d4,stroke:#0891b2,color:#fff
    style C3 fill:#06b6d4,stroke:#0891b2,color:#fff
    style C4 fill:#06b6d4,stroke:#0891b2,color:#fff

    style A1 fill:#3b82f6,stroke:#60a5fa,color:#fff
    style A2 fill:#3b82f6,stroke:#60a5fa,color:#fff
    style A3 fill:#3b82f6,stroke:#60a5fa,color:#fff
    style A4 fill:#3b82f6,stroke:#60a5fa,color:#fff
    style A5 fill:#3b82f6,stroke:#60a5fa,color:#fff
    style A6 fill:#3b82f6,stroke:#60a5fa,color:#fff
    style A7 fill:#3b82f6,stroke:#60a5fa,color:#fff

    style B1 fill:#8b5cf6,stroke:#a855f7,color:#fff
    style B2 fill:#8b5cf6,stroke:#a855f7,color:#fff
    style B3 fill:#8b5cf6,stroke:#a855f7,color:#fff
    style B4 fill:#8b5cf6,stroke:#a855f7,color:#fff
    style B5 fill:#8b5cf6,stroke:#a855f7,color:#fff

    style D1 fill:#22c55e,stroke:#16a34a,color:#fff
    style D2 fill:#22c55e,stroke:#16a34a,color:#fff
    style D3 fill:#22c55e,stroke:#16a34a,color:#fff
                </div>
            </div>

            <!-- 系统协调机制说明 -->
            <div class="mb-12 card-hover rounded-2xl p-8">
                <h3 class="text-2xl font-bold text-center mb-6 text-cyan-400">
                    <i class="fas fa-brain mr-3"></i>中枢大模型协调机制
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-gradient-to-br from-cyan-400 to-cyan-600 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-sitemap text-white text-xl"></i>
                        </div>
                        <h4 class="text-lg font-bold text-cyan-400 mb-2">统一调度</h4>
                        <p class="text-gray-300 text-sm">协调投放和运营 Agent 的任务分配与执行优先级</p>
                    </div>
                    <div class="text-center">
                        <div class="w-16 h-16 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-exchange-alt text-white text-xl"></i>
                        </div>
                        <h4 class="text-lg font-bold text-blue-400 mb-2">数据融合</h4>
                        <p class="text-gray-300 text-sm">整合投放数据和用户行为数据，形成统一决策依据</p>
                    </div>
                    <div class="text-center">
                        <div class="w-16 h-16 bg-gradient-to-br from-purple-400 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-cogs text-white text-xl"></i>
                        </div>
                        <h4 class="text-lg font-bold text-purple-400 mb-2">智能决策</h4>
                        <p class="text-gray-300 text-sm">基于全局数据进行最优策略制定和实时调整</p>
                    </div>
                    <div class="text-center">
                        <div class="w-16 h-16 bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-sync-alt text-white text-xl"></i>
                        </div>
                        <h4 class="text-lg font-bold text-green-400 mb-2">协同优化</h4>
                        <p class="text-gray-300 text-sm">确保投放和运营策略的协同一致，最大化整体效果</p>
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- 投放 Agent 概览 -->
                <div class="card-hover rounded-2xl p-8">
                    <div class="flex items-center mb-6">
                        <i class="fas fa-bullhorn text-3xl text-blue-400 mr-4"></i>
                        <h3 class="text-2xl font-bold text-white">投放 Agent</h3>
                        <span class="ml-3 px-3 py-1 bg-blue-500/20 text-blue-400 text-sm rounded-full">7大核心功能</span>
                    </div>
                    <div class="space-y-4">
                        <div class="flex items-center">
                            <i class="fas fa-spider text-blue-400 mr-3"></i>
                            <span>24小时热门素材爬虫，智能分类整理</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-industry text-blue-400 mr-3"></i>
                            <span>素材工厂：一键生成多类型素材，支持多语言本地化</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-flask text-blue-400 mr-3"></i>
                            <span>创意实验室：脚本生成+上线前预测</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-globe text-blue-400 mr-3"></i>
                            <span>多平台投放：抖音、Facebook、Unity Ads等</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-chart-line text-blue-400 mr-3"></i>
                            <span>归因分析：CPI、ROAS、LTV等关键指标</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-robot text-blue-400 mr-3"></i>
                            <span>自动调量：基于ROI表现智能调整预算</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-crystal-ball text-blue-400 mr-3"></i>
                            <span>ROI-Oracle：回本预测与成功概率评估</span>
                        </div>
                    </div>
                    <div class="mt-6 p-4 bg-blue-500/10 rounded-lg border border-blue-400/20">
                        <div class="text-center">
                            <div class="text-2xl font-bold text-blue-400">85%</div>
                            <div class="text-sm text-gray-400">投放决策准确率 (vs 人工60%)</div>
                        </div>
                    </div>
                </div>

                <!-- 运营 Agent 概览 -->
                <div class="card-hover rounded-2xl p-8">
                    <div class="flex items-center mb-6">
                        <i class="fas fa-users text-3xl text-purple-400 mr-4"></i>
                        <h3 class="text-2xl font-bold text-white">运营 Agent</h3>
                        <span class="ml-3 px-3 py-1 bg-purple-500/20 text-purple-400 text-sm rounded-full">智能运营</span>
                    </div>
                    <div class="space-y-4">
                        <div class="flex items-center">
                            <i class="fas fa-user-chart text-purple-400 mr-3"></i>
                            <span>用户洞察：分群分析+社群情绪监测</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-calendar-alt text-purple-400 mr-3"></i>
                            <span>活动设计器：拖拽式排期+自动奖励生成</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-exclamation-triangle text-purple-400 mr-3"></i>
                            <span>流失预警++：提前识别+自动回流礼包</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-robot text-purple-400 mr-3"></i>
                            <span>客服机器人：FAQ检索+大模型智能回复</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-tools text-purple-400 mr-3"></i>
                            <span>专业化工具：众多小模型和辅助工具集</span>
                        </div>
                    </div>
                    <div class="mt-6 grid grid-cols-2 gap-4">
                        <div class="p-3 bg-purple-500/10 rounded-lg border border-purple-400/20 text-center">
                            <div class="text-xl font-bold text-purple-400">85%+</div>
                            <div class="text-xs text-gray-400">流失预测准确率</div>
                        </div>
                        <div class="p-3 bg-purple-500/10 rounded-lg border border-purple-400/20 text-center">
                            <div class="text-xl font-bold text-purple-400">99%</div>
                            <div class="text-xs text-gray-400">风控识别率</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 功能详解 -->
    <section id="features" class="py-20 bg-gradient-to-b from-slate-900/50 to-slate-800/50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-4xl md:text-5xl font-bold mb-6 gradient-text">核心功能详解</h2>
                <p class="text-xl text-gray-300">深度解析投放 Agent 和运营 Agent 的专业能力</p>
            </div>

            <!-- 投放Agent七大功能模块 -->
            <div class="mb-20">
                <h3 class="text-3xl font-bold text-center mb-12 text-blue-400">
                    <i class="fas fa-bullhorn mr-3"></i>投放 Agent 七大核心功能
                </h3>

                <!-- 第一行：热门素材爬虫、素材工厂、创意实验室 -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-8">
                    <!-- 1. 热门素材爬虫 -->
                    <div class="card-hover rounded-xl p-6">
                        <div class="text-center mb-4">
                            <i class="fas fa-spider text-4xl text-blue-400 mb-4"></i>
                            <h4 class="text-xl font-bold text-white">1. 热门素材爬虫</h4>
                            <p class="text-sm text-gray-400">24小时自动抓取与智能分类</p>
                        </div>
                        <ul class="space-y-2 text-gray-300">
                            <li><i class="fas fa-check text-green-400 mr-2"></i>24小时自动抓取各大广告库热门创意</li>
                            <li><i class="fas fa-check text-green-400 mr-2"></i>智能分类整理：游戏类型、风格、平台</li>
                            <li><i class="fas fa-check text-green-400 mr-2"></i>热度趋势分析与排行榜生成</li>
                            <li><i class="fas fa-check text-green-400 mr-2"></i>竞品素材监控与对比分析</li>
                            <li><i class="fas fa-check text-green-400 mr-2"></i>支持Facebook、抖音、Unity等主流平台</li>
                        </ul>
                    </div>

                    <!-- 2. 素材工厂 -->
                    <div class="card-hover rounded-xl p-6">
                        <div class="text-center mb-4">
                            <i class="fas fa-industry text-4xl text-blue-400 mb-4"></i>
                            <h4 class="text-xl font-bold text-white">2. 素材工厂</h4>
                            <p class="text-sm text-gray-400">一键生成多类型素材</p>
                        </div>
                        <ul class="space-y-2 text-gray-300">
                            <li><i class="fas fa-check text-green-400 mr-2"></i>一键生成图片、短视频、可玩广告</li>
                            <li><i class="fas fa-check text-green-400 mr-2"></i>支持多语言本地化适配</li>
                            <li><i class="fas fa-check text-green-400 mr-2"></i>Sora级别视频生成技术</li>
                            <li><i class="fas fa-check text-green-400 mr-2"></i>自动尺寸适配：横版、竖版、方形</li>
                            <li><i class="fas fa-check text-green-400 mr-2"></i>批量生成与版本管理</li>
                        </ul>
                    </div>

                    <!-- 3. 创意实验室 -->
                    <div class="card-hover rounded-xl p-6">
                        <div class="text-center mb-4">
                            <i class="fas fa-flask text-4xl text-blue-400 mb-4"></i>
                            <h4 class="text-xl font-bold text-white">3. 创意实验室</h4>
                            <p class="text-sm text-gray-400">脚本生成+上线前预测</p>
                        </div>
                        <div class="space-y-3">
                            <div class="bg-blue-500/10 rounded-lg p-3 border border-blue-400/20">
                                <h5 class="font-semibold text-blue-400 mb-2">脚本生成器</h5>
                                <ul class="space-y-1 text-sm text-gray-300">
                                    <li><i class="fas fa-check text-green-400 mr-2"></i>基于ChatGPT自动生成广告分镜脚本</li>
                                    <li><i class="fas fa-check text-green-400 mr-2"></i>支持多种创意风格和表现形式</li>
                                </ul>
                            </div>
                            <div class="bg-purple-500/10 rounded-lg p-3 border border-purple-400/20">
                                <h5 class="font-semibold text-purple-400 mb-2">上线前预测</h5>
                                <ul class="space-y-1 text-sm text-gray-300">
                                    <li><i class="fas fa-check text-green-400 mr-2"></i>CQS创意质量评分</li>
                                    <li><i class="fas fa-check text-green-400 mr-2"></i>疲劳度分析预测</li>
                                    <li><i class="fas fa-check text-green-400 mr-2"></i>违规风险评估</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 第二行：多平台投放、归因分析 -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
                    <!-- 4. 多平台投放 -->
                    <div class="card-hover rounded-xl p-6">
                        <div class="text-center mb-4">
                            <i class="fas fa-globe text-4xl text-blue-400 mb-4"></i>
                            <h4 class="text-xl font-bold text-white">4. 多平台投放</h4>
                            <p class="text-sm text-gray-400">自动化广告投放管理</p>
                        </div>
                        <ul class="space-y-2 text-gray-300">
                            <li><i class="fas fa-check text-green-400 mr-2"></i>自动化计划创建与配置</li>
                            <li><i class="fas fa-check text-green-400 mr-2"></i>智能人群定向与兴趣标签</li>
                            <li><i class="fas fa-check text-green-400 mr-2"></i>动态出价策略调整</li>
                            <li><i class="fas fa-check text-green-400 mr-2"></i>支持抖音/巨量引擎、Facebook、Unity Ads</li>
                            <li><i class="fas fa-check text-green-400 mr-2"></i>跨平台数据同步与统一管理</li>
                            <li><i class="fas fa-check text-green-400 mr-2"></i>异常监控与自动暂停保护</li>
                        </ul>
                    </div>

                    <!-- 5. 归因分析 -->
                    <div class="card-hover rounded-xl p-6">
                        <div class="text-center mb-4">
                            <i class="fas fa-chart-line text-4xl text-blue-400 mb-4"></i>
                            <h4 class="text-xl font-bold text-white">5. 归因分析</h4>
                            <p class="text-sm text-gray-400">关键指标汇总与分析</p>
                        </div>
                        <ul class="space-y-2 text-gray-300">
                            <li><i class="fas fa-check text-green-400 mr-2"></i>CPI（每安装成本）实时监控</li>
                            <li><i class="fas fa-check text-green-400 mr-2"></i>ROAS（广告支出回报率）分析</li>
                            <li><i class="fas fa-check text-green-400 mr-2"></i>LTV（用户生命周期价值）预测</li>
                            <li><i class="fas fa-check text-green-400 mr-2"></i>渠道效果对比与排名</li>
                            <li><i class="fas fa-check text-green-400 mr-2"></i>素材效果归因分析</li>
                            <li><i class="fas fa-check text-green-400 mr-2"></i>自定义报表与数据导出</li>
                        </ul>
                    </div>
                </div>

                <!-- 第三行：自动调量、ROI-Oracle -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <!-- 6. 自动调量 -->
                    <div class="card-hover rounded-xl p-6">
                        <div class="text-center mb-4">
                            <i class="fas fa-robot text-4xl text-blue-400 mb-4"></i>
                            <h4 class="text-xl font-bold text-white">6. 自动调量</h4>
                            <p class="text-sm text-gray-400">基于ROI表现智能调整</p>
                        </div>
                        <ul class="space-y-2 text-gray-300">
                            <li><i class="fas fa-check text-green-400 mr-2"></i>基于ROI表现自动调整投放预算</li>
                            <li><i class="fas fa-check text-green-400 mr-2"></i>ROI不达标时自动减量保护</li>
                            <li><i class="fas fa-check text-green-400 mr-2"></i>超过目标ROI时自动加量扩量</li>
                            <li><i class="fas fa-check text-green-400 mr-2"></i>多维度调量策略：时段、地域、人群</li>
                            <li><i class="fas fa-check text-green-400 mr-2"></i>风险控制与预算上限保护</li>
                            <li><i class="fas fa-check text-green-400 mr-2"></i>调量日志与决策透明化</li>
                        </ul>
                    </div>

                    <!-- 7. ROI-Oracle回本预测 -->
                    <div class="card-hover rounded-xl p-6">
                        <div class="text-center mb-4">
                            <i class="fas fa-crystal-ball text-4xl text-blue-400 mb-4"></i>
                            <h4 class="text-xl font-bold text-white">7. ROI-Oracle 回本预测</h4>
                            <p class="text-sm text-gray-400">基于历史数据的智能预测</p>
                        </div>
                        <ul class="space-y-2 text-gray-300">
                            <li><i class="fas fa-check text-green-400 mr-2"></i>基于公司历史项目数据训练</li>
                            <li><i class="fas fa-check text-green-400 mr-2"></i>预测项目回本周期与时间节点</li>
                            <li><i class="fas fa-check text-green-400 mr-2"></i>成功概率评估与风险等级</li>
                            <li><i class="fas fa-check text-green-400 mr-2"></i>不同投放策略的ROI对比</li>
                            <li><i class="fas fa-check text-green-400 mr-2"></i>市场环境因素影响分析</li>
                            <li><i class="fas fa-check text-green-400 mr-2"></i>投资决策建议与优化方案</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- 运营Agent核心功能模块 -->
            <div>
                <h3 class="text-3xl font-bold text-center mb-12 text-purple-400">
                    <i class="fas fa-users mr-3"></i>运营 Agent 核心功能模块
                </h3>

                <!-- 第一行：用户洞察、活动设计器 -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
                    <!-- 1. 用户洞察 -->
                    <div class="card-hover rounded-xl p-6">
                        <div class="text-center mb-4">
                            <i class="fas fa-user-chart text-4xl text-purple-400 mb-4"></i>
                            <h4 class="text-xl font-bold text-white">1. 用户洞察</h4>
                            <p class="text-sm text-gray-400">分群分析与情绪监测</p>
                        </div>
                        <div class="space-y-3">
                            <div class="bg-purple-500/10 rounded-lg p-3 border border-purple-400/20">
                                <h5 class="font-semibold text-purple-400 mb-2">用户分群分析</h5>
                                <ul class="space-y-1 text-sm text-gray-300">
                                    <li><i class="fas fa-check text-green-400 mr-2"></i>实时用户画像生成与更新</li>
                                    <li><i class="fas fa-check text-green-400 mr-2"></i>生命周期分层管理</li>
                                    <li><i class="fas fa-check text-green-400 mr-2"></i>LTV价值评估与预测</li>
                                    <li><i class="fas fa-check text-green-400 mr-2"></i>高价值用户自动识别</li>
                                </ul>
                            </div>
                            <div class="bg-cyan-500/10 rounded-lg p-3 border border-cyan-400/20">
                                <h5 class="font-semibold text-cyan-400 mb-2">社群情绪监测</h5>
                                <ul class="space-y-1 text-sm text-gray-300">
                                    <li><i class="fas fa-check text-green-400 mr-2"></i>社交媒体情绪分析</li>
                                    <li><i class="fas fa-check text-green-400 mr-2"></i>玩家反馈情感识别</li>
                                    <li><i class="fas fa-check text-green-400 mr-2"></i>舆情预警与风险评估</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- 2. 活动设计器 -->
                    <div class="card-hover rounded-xl p-6">
                        <div class="text-center mb-4">
                            <i class="fas fa-calendar-alt text-4xl text-purple-400 mb-4"></i>
                            <h4 class="text-xl font-bold text-white">2. 活动设计器</h4>
                            <p class="text-sm text-gray-400">拖拽式排期与自动生成</p>
                        </div>
                        <div class="space-y-3">
                            <div class="bg-purple-500/10 rounded-lg p-3 border border-purple-400/20">
                                <h5 class="font-semibold text-purple-400 mb-2">拖拽式活动排期</h5>
                                <ul class="space-y-1 text-sm text-gray-300">
                                    <li><i class="fas fa-check text-green-400 mr-2"></i>可视化活动时间轴设计</li>
                                    <li><i class="fas fa-check text-green-400 mr-2"></i>多活动并行管理</li>
                                    <li><i class="fas fa-check text-green-400 mr-2"></i>冲突检测与优化建议</li>
                                    <li><i class="fas fa-check text-green-400 mr-2"></i>模板库与快速复用</li>
                                </ul>
                            </div>
                            <div class="bg-green-500/10 rounded-lg p-3 border border-green-400/20">
                                <h5 class="font-semibold text-green-400 mb-2">自动奖励生成</h5>
                                <ul class="space-y-1 text-sm text-gray-300">
                                    <li><i class="fas fa-check text-green-400 mr-2"></i>基于用户价值智能配奖</li>
                                    <li><i class="fas fa-check text-green-400 mr-2"></i>奖励效果预测与优化</li>
                                    <li><i class="fas fa-check text-green-400 mr-2"></i>成本控制与ROI平衡</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 第二行：流失预警++、客服机器人 -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
                    <!-- 3. 流失预警++ -->
                    <div class="card-hover rounded-xl p-6">
                        <div class="text-center mb-4">
                            <i class="fas fa-exclamation-triangle text-4xl text-purple-400 mb-4"></i>
                            <h4 class="text-xl font-bold text-white">3. 流失预警++</h4>
                            <p class="text-sm text-gray-400">提前识别与自动挽回</p>
                        </div>
                        <ul class="space-y-2 text-gray-300">
                            <li><i class="fas fa-check text-green-400 mr-2"></i>提前识别潜在流失用户</li>
                            <li><i class="fas fa-check text-green-400 mr-2"></i>流失原因智能分析与分类</li>
                            <li><i class="fas fa-check text-green-400 mr-2"></i>自动推送个性化回流礼包</li>
                            <li><i class="fas fa-check text-green-400 mr-2"></i>定制化挽回任务生成</li>
                            <li><i class="fas fa-check text-green-400 mr-2"></i>挽回效果跟踪与优化</li>
                            <li><i class="fas fa-check text-green-400 mr-2"></i>85%+预测准确率，提前3天预警</li>
                        </ul>
                    </div>

                    <!-- 4. 客服机器人 -->
                    <div class="card-hover rounded-xl p-6">
                        <div class="text-center mb-4">
                            <i class="fas fa-robot text-4xl text-purple-400 mb-4"></i>
                            <h4 class="text-xl font-bold text-white">4. 客服机器人</h4>
                            <p class="text-sm text-gray-400">FAQ检索+大模型智能回复</p>
                        </div>
                        <div class="space-y-3">
                            <div class="bg-purple-500/10 rounded-lg p-3 border border-purple-400/20">
                                <h5 class="font-semibold text-purple-400 mb-2">FAQ检索系统</h5>
                                <ul class="space-y-1 text-sm text-gray-300">
                                    <li><i class="fas fa-check text-green-400 mr-2"></i>智能问题匹配与检索</li>
                                    <li><i class="fas fa-check text-green-400 mr-2"></i>多轮对话上下文理解</li>
                                    <li><i class="fas fa-check text-green-400 mr-2"></i>知识库自动更新维护</li>
                                </ul>
                            </div>
                            <div class="bg-blue-500/10 rounded-lg p-3 border border-blue-400/20">
                                <h5 class="font-semibold text-blue-400 mb-2">大模型智能回复</h5>
                                <ul class="space-y-1 text-sm text-gray-300">
                                    <li><i class="fas fa-check text-green-400 mr-2"></i>复杂问题智能理解与回复</li>
                                    <li><i class="fas fa-check text-green-400 mr-2"></i>情感识别与个性化回应</li>
                                    <li><i class="fas fa-check text-green-400 mr-2"></i>复杂问题自动转人工</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 第三行：其他专业化工具 -->
                <div class="grid grid-cols-1 gap-8">
                    <!-- 5. 其他专业化工具 -->
                    <div class="card-hover rounded-xl p-6">
                        <div class="text-center mb-6">
                            <i class="fas fa-tools text-4xl text-purple-400 mb-4"></i>
                            <h4 class="text-xl font-bold text-white">5. 其他专业化工具集</h4>
                            <p class="text-sm text-gray-400">众多小模型和辅助工具</p>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                            <div class="bg-gradient-to-b from-red-500/20 to-pink-500/20 rounded-lg p-4 border border-red-400/20">
                                <div class="text-center">
                                    <i class="fas fa-shield-alt text-red-400 text-2xl mb-2"></i>
                                    <h5 class="font-semibold text-red-400 mb-2">智能风控</h5>
                                    <ul class="space-y-1 text-xs text-gray-300">
                                        <li>内容审核99%准确率</li>
                                        <li>公会诈骗行为检测</li>
                                        <li>异常行为模式识别</li>
                                        <li>误杀率控制<0.5%</li>
                                    </ul>
                                </div>
                            </div>
                            <div class="bg-gradient-to-b from-green-500/20 to-teal-500/20 rounded-lg p-4 border border-green-400/20">
                                <div class="text-center">
                                    <i class="fas fa-chart-pie text-green-400 text-2xl mb-2"></i>
                                    <h5 class="font-semibold text-green-400 mb-2">数据分析</h5>
                                    <ul class="space-y-1 text-xs text-gray-300">
                                        <li>实时数据看板</li>
                                        <li>自定义报表生成</li>
                                        <li>趋势预测分析</li>
                                        <li>异常数据预警</li>
                                    </ul>
                                </div>
                            </div>
                            <div class="bg-gradient-to-b from-yellow-500/20 to-orange-500/20 rounded-lg p-4 border border-yellow-400/20">
                                <div class="text-center">
                                    <i class="fas fa-comments text-yellow-400 text-2xl mb-2"></i>
                                    <h5 class="font-semibold text-yellow-400 mb-2">社区管理</h5>
                                    <ul class="space-y-1 text-xs text-gray-300">
                                        <li>社区内容监控</li>
                                        <li>KOL影响力分析</li>
                                        <li>话题热度追踪</li>
                                        <li>舆情危机预警</li>
                                    </ul>
                                </div>
                            </div>
                            <div class="bg-gradient-to-b from-indigo-500/20 to-purple-500/20 rounded-lg p-4 border border-indigo-400/20">
                                <div class="text-center">
                                    <i class="fas fa-cog text-indigo-400 text-2xl mb-2"></i>
                                    <h5 class="font-semibold text-indigo-400 mb-2">运营自动化</h5>
                                    <ul class="space-y-1 text-xs text-gray-300">
                                        <li>任务自动分配</li>
                                        <li>工作流程优化</li>
                                        <li>效率监控分析</li>
                                        <li>智能排班系统</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 市场数据与竞品分析 -->
    <section class="py-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-4xl md:text-5xl font-bold mb-6 gradient-text">市场洞察与竞争优势</h2>
                <p class="text-xl text-gray-300">基于最新市场数据的战略分析</p>
            </div>

            <!-- 市场数据 -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
                <div class="card-hover rounded-2xl p-8">
                    <h3 class="text-2xl font-bold text-center mb-8 text-cyan-400">
                        <i class="fas fa-chart-bar mr-3"></i>市场规模预测
                    </h3>
                    <div class="space-y-6">
                        <div>
                            <div class="flex justify-between items-center mb-2">
                                <span class="text-gray-300">2025-2029年AI游戏市场增长</span>
                                <span class="text-green-400 font-bold">+2000亿</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 95%;"></div>
                            </div>
                        </div>
                        <div>
                            <div class="flex justify-between items-center mb-2">
                                <span class="text-gray-300">中国游戏发行市场规模</span>
                                <span class="text-blue-400 font-bold">1000亿+</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 88%;"></div>
                            </div>
                        </div>
                        <div>
                            <div class="flex justify-between items-center mb-2">
                                <span class="text-gray-300">目标市场份额（3年）</span>
                                <span class="text-purple-400 font-bold">5%</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 60%;"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card-hover rounded-2xl p-8">
                    <h3 class="text-2xl font-bold text-center mb-8 text-yellow-400">
                        <i class="fas fa-trophy mr-3"></i>技术优势对比
                    </h3>
                    <div class="space-y-4">
                        <div class="flex justify-between items-center p-3 bg-green-500/20 rounded-lg">
                            <span class="text-gray-300">素材生成速度</span>
                            <span class="text-green-400 font-bold">2小时 vs 2天</span>
                        </div>
                        <div class="flex justify-between items-center p-3 bg-blue-500/20 rounded-lg">
                            <span class="text-gray-300">投放决策准确率</span>
                            <span class="text-blue-400 font-bold">85% vs 60%</span>
                        </div>
                        <div class="flex justify-between items-center p-3 bg-purple-500/20 rounded-lg">
                            <span class="text-gray-300">用户流失预测</span>
                            <span class="text-purple-400 font-bold">提前3天 85%</span>
                        </div>
                        <div class="flex justify-between items-center p-3 bg-cyan-500/20 rounded-lg">
                            <span class="text-gray-300">风控识别准确率</span>
                            <span class="text-cyan-400 font-bold">99% 误杀<0.5%</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 竞品分析表格 -->
            <div class="card-hover rounded-2xl p-8">
                <h3 class="text-2xl font-bold text-center mb-8 text-orange-400">
                    <i class="fas fa-balance-scale mr-3"></i>竞品分析矩阵
                </h3>
                <div class="overflow-x-auto">
                    <table class="w-full text-left">
                        <thead>
                            <tr class="border-b border-gray-600">
                                <th class="py-3 px-4 text-gray-300">公司/产品</th>
                                <th class="py-3 px-4 text-gray-300">主要方向</th>
                                <th class="py-3 px-4 text-gray-300">核心能力</th>
                                <th class="py-3 px-4 text-gray-300">我们的机会</th>
                            </tr>
                        </thead>
                        <tbody class="space-y-2">
                            <tr class="border-b border-gray-700/50">
                                <td class="py-3 px-4 text-blue-400 font-semibold">腾讯 Game AIR</td>
                                <td class="py-3 px-4 text-gray-300">全流程AI赋能平台</td>
                                <td class="py-3 px-4 text-gray-300">资源丰富，技术领先</td>
                                <td class="py-3 px-4 text-green-400">轻量化部署，中小厂商友好</td>
                            </tr>
                            <tr class="border-b border-gray-700/50">
                                <td class="py-3 px-4 text-purple-400 font-semibold">网易 伏羲</td>
                                <td class="py-3 px-4 text-gray-300">AI NPC，《逆水寒》沈秋索</td>
                                <td class="py-3 px-4 text-gray-300">AI原生游戏探索</td>
                                <td class="py-3 px-4 text-green-400">发行运营聚焦，商业价值明确</td>
                            </tr>
                            <tr class="border-b border-gray-700/50">
                                <td class="py-3 px-4 text-cyan-400 font-semibold">米哈游</td>
                                <td class="py-3 px-4 text-gray-300">AI造梦，原生游戏体验</td>
                                <td class="py-3 px-4 text-gray-300">创新能力强</td>
                                <td class="py-3 px-4 text-green-400">标准化产品，快速复制</td>
                            </tr>
                            <tr class="border-b border-gray-700/50">
                                <td class="py-3 px-4 text-yellow-400 font-semibold">NVIDIA ACE</td>
                                <td class="py-3 px-4 text-gray-300">AI角色技术，PUBG、inZOI采用</td>
                                <td class="py-3 px-4 text-gray-300">底层技术强</td>
                                <td class="py-3 px-4 text-green-400">完整发行解决方案</td>
                            </tr>
                            <tr class="border-b border-gray-700/50 bg-gradient-to-r from-pink-500/10 to-orange-500/10">
                                <td class="py-3 px-4 text-pink-400 font-semibold">Sett AI</td>
                                <td class="py-3 px-4 text-gray-300">AI游戏广告视频生成</td>
                                <td class="py-3 px-4 text-gray-300">2700万美元融资，Agentic AI + 游戏引擎</td>
                                <td class="py-3 px-4 text-green-400">更全面的发行闭环</td>
                            </tr>
                            <tr class="border-b border-gray-700/50 bg-gradient-to-r from-indigo-500/10 to-blue-500/10">
                                <td class="py-3 px-4 text-indigo-400 font-semibold">Pencil Pro</td>
                                <td class="py-3 px-4 text-gray-300">AI广告创意生成平台</td>
                                <td class="py-3 px-4 text-gray-300">100万+创意执行，$10亿+媒体支出数据</td>
                                <td class="py-3 px-4 text-green-400">游戏垂直化专精</td>
                            </tr>
                            <tr class="border-b border-gray-700/50 bg-gradient-to-r from-green-500/10 to-teal-500/10">
                                <td class="py-3 px-4 text-green-400 font-semibold">VeoGo AI</td>
                                <td class="py-3 px-4 text-gray-300">视频播放量预测工具</td>
                                <td class="py-3 px-4 text-gray-300">92%+预测准确率，多平台支持</td>
                                <td class="py-3 px-4 text-green-400">投放+运营一体化</td>
                            </tr>
                            <tr class="border-b border-gray-700/50 bg-gradient-to-r from-purple-500/10 to-pink-500/10">
                                <td class="py-3 px-4 text-purple-400 font-semibold">美图 奇觅</td>
                                <td class="py-3 px-4 text-gray-300">游戏广告AI制作与投放</td>
                                <td class="py-3 px-4 text-gray-300">一站式游戏广告平台，多渠道投放</td>
                                <td class="py-3 px-4 text-green-400">更智能的预测算法</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <!-- 重点工具详解 -->
                <div class="mt-12 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <div class="bg-gradient-to-b from-pink-500/20 to-orange-500/20 rounded-xl p-6 border border-pink-400/20">
                        <div class="flex items-center mb-4">
                            <i class="fas fa-video text-pink-400 text-2xl mr-3"></i>
                            <h4 class="text-lg font-bold text-white">Sett AI</h4>
                        </div>
                        <div class="space-y-2 text-sm">
                            <div class="text-gray-300">• 2700万美元种子轮融资</div>
                            <div class="text-gray-300">• Agentic AI + 游戏引擎技术</div>
                            <div class="text-gray-300">• 专注游戏营销视频生成</div>
                            <div class="text-pink-400">移动游戏营销的AI革命</div>
                        </div>
                    </div>

                    <div class="bg-gradient-to-b from-indigo-500/20 to-blue-500/20 rounded-xl p-6 border border-indigo-400/20">
                        <div class="flex items-center mb-4">
                            <i class="fas fa-palette text-indigo-400 text-2xl mr-3"></i>
                            <h4 class="text-lg font-bold text-white">Pencil Pro</h4>
                        </div>
                        <div class="space-y-2 text-sm">
                            <div class="text-gray-300">• 全球最大生成式AI SaaS平台</div>
                            <div class="text-gray-300">• 100万+创意执行/年</div>
                            <div class="text-gray-300">• 集成OpenAI、Stable Diffusion</div>
                            <div class="text-indigo-400">企业级广告创意解决方案</div>
                        </div>
                    </div>

                    <div class="bg-gradient-to-b from-green-500/20 to-teal-500/20 rounded-xl p-6 border border-green-400/20">
                        <div class="flex items-center mb-4">
                            <i class="fas fa-chart-line text-green-400 text-2xl mr-3"></i>
                            <h4 class="text-lg font-bold text-white">VeoGo AI</h4>
                        </div>
                        <div class="space-y-2 text-sm">
                            <div class="text-gray-300">• 92%+流量预测准确率</div>
                            <div class="text-gray-300">• 支持抖音、小红书等平台</div>
                            <div class="text-gray-300">• 智能逐帧分析技术</div>
                            <div class="text-green-400">破解"算法黑箱"的利器</div>
                        </div>
                    </div>

                    <div class="bg-gradient-to-b from-purple-500/20 to-pink-500/20 rounded-xl p-6 border border-purple-400/20">
                        <div class="flex items-center mb-4">
                            <i class="fas fa-gamepad text-purple-400 text-2xl mr-3"></i>
                            <h4 class="text-lg font-bold text-white">美图 奇觅</h4>
                        </div>
                        <div class="space-y-2 text-sm">
                            <div class="text-gray-300">• 游戏垂直化AI平台</div>
                            <div class="text-gray-300">• 实时热点捕捉</div>
                            <div class="text-gray-300">• 多渠道广告投放管理</div>
                            <div class="text-purple-400">游戏营销突围的专业助手</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 技术愿景 -->
    <section id="vision" class="py-20 bg-gradient-to-b from-slate-800/50 to-slate-900/50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-4xl md:text-5xl font-bold mb-6 gradient-text">技术发展愿景</h2>
                <p class="text-xl text-gray-300">从基础能力到智能化的技术演进路径</p>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- 基础阶段 -->
                <div class="timeline-item">
                    <div class="card-hover rounded-xl p-8 h-full">
                        <div class="text-center mb-6">
                            <div class="w-16 h-16 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-foundation text-white text-xl"></i>
                            </div>
                            <h3 class="text-xl font-bold text-blue-400">基础能力建设</h3>
                            <p class="text-gray-400">核心模块构建</p>
                        </div>
                        <div class="space-y-3">
                            <div class="flex items-center">
                                <i class="fas fa-check-circle text-green-400 mr-3"></i>
                                <span class="text-sm">基础素材生成能力</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-check-circle text-green-400 mr-3"></i>
                                <span class="text-sm">单平台投放框架</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-check-circle text-green-400 mr-3"></i>
                                <span class="text-sm">用户行为分析模型</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-check-circle text-green-400 mr-3"></i>
                                <span class="text-sm">基础数据报表系统</span>
                            </div>
                        </div>
                        <div class="mt-6 text-center">
                            <div class="text-lg font-bold text-blue-400">核心验证</div>
                            <div class="text-sm text-gray-400">技术可行性确认</div>
                        </div>
                    </div>
                </div>

                <!-- 完整阶段 -->
                <div class="timeline-item">
                    <div class="card-hover rounded-xl p-8 h-full">
                        <div class="text-center mb-6">
                            <div class="w-16 h-16 bg-gradient-to-br from-purple-400 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-cogs text-white text-xl"></i>
                            </div>
                            <h3 class="text-xl font-bold text-purple-400">系统完整化</h3>
                            <p class="text-gray-400">全功能集成</p>
                        </div>
                        <div class="space-y-3">
                            <div class="flex items-center">
                                <i class="fas fa-video text-purple-400 mr-3"></i>
                                <span class="text-sm">多媒体内容生成</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-network-wired text-purple-400 mr-3"></i>
                                <span class="text-sm">多平台统一管控</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-user-friends text-purple-400 mr-3"></i>
                                <span class="text-sm">个性化营销引擎</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-robot text-purple-400 mr-3"></i>
                                <span class="text-sm">智能化客服体系</span>
                            </div>
                        </div>
                        <div class="mt-6 text-center">
                            <div class="text-lg font-bold text-purple-400">功能完备</div>
                            <div class="text-sm text-gray-400">端到端解决方案</div>
                        </div>
                    </div>
                </div>

                <!-- 智能阶段 -->
                <div class="timeline-item">
                    <div class="card-hover rounded-xl p-8 h-full">
                        <div class="text-center mb-6">
                            <div class="w-16 h-16 bg-gradient-to-br from-cyan-400 to-cyan-600 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-brain text-white text-xl"></i>
                            </div>
                            <h3 class="text-xl font-bold text-cyan-400">智能化演进</h3>
                            <p class="text-gray-400">自主决策能力</p>
                        </div>
                        <div class="space-y-3">
                            <div class="flex items-center">
                                <i class="fas fa-globe text-cyan-400 mr-3"></i>
                                <span class="text-sm">全球化智能适配</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-magic text-cyan-400 mr-3"></i>
                                <span class="text-sm">自主创意生成</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-chart-line text-cyan-400 mr-3"></i>
                                <span class="text-sm">预测性决策优化</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-infinity text-cyan-400 mr-3"></i>
                                <span class="text-sm">持续自我演进</span>
                            </div>
                        </div>
                        <div class="mt-6 text-center">
                            <div class="text-lg font-bold text-cyan-400">智能自主</div>
                            <div class="text-sm text-gray-400">AI原生发行</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 技术架构演进 -->
            <div class="mt-16 card-hover rounded-2xl p-8 text-center">
                <h3 class="text-2xl font-bold mb-8 text-yellow-400">
                    <i class="fas fa-sitemap mr-3"></i>技术架构演进方向
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                    <div>
                        <div class="text-2xl font-bold text-blue-400 mb-2">
                            <i class="fas fa-database"></i>
                        </div>
                        <div class="text-lg font-semibold text-blue-400 mb-1">数据驱动</div>
                        <div class="text-gray-400 text-sm">全链路数据采集与分析</div>
                    </div>
                    <div>
                        <div class="text-2xl font-bold text-green-400 mb-2">
                            <i class="fas fa-microchip"></i>
                        </div>
                        <div class="text-lg font-semibold text-green-400 mb-1">模型优化</div>
                        <div class="text-gray-400 text-sm">持续学习与模型迭代</div>
                    </div>
                    <div>
                        <div class="text-2xl font-bold text-purple-400 mb-2">
                            <i class="fas fa-cloud"></i>
                        </div>
                        <div class="text-lg font-semibold text-purple-400 mb-1">云原生</div>
                        <div class="text-gray-400 text-sm">弹性扩展与边缘计算</div>
                    </div>
                    <div>
                        <div class="text-2xl font-bold text-cyan-400 mb-2">
                            <i class="fas fa-sync-alt"></i>
                        </div>
                        <div class="text-lg font-semibold text-cyan-400 mb-1">自适应</div>
                        <div class="text-gray-400 text-sm">实时策略调整与优化</div>
                    </div>
                </div>
            </div>

            <!-- 未来展望 -->
            <div class="mt-16 card-hover rounded-2xl p-8 bg-gradient-to-r from-blue-500/10 to-purple-500/10">
                <h3 class="text-2xl font-bold text-center mb-6 gradient-text">
                    <i class="fas fa-telescope mr-3"></i>终极愿景
                </h3>
                <div class="text-center">
                    <p class="text-lg text-gray-300 mb-6 leading-relaxed">
                        构建一个<span class="text-blue-400 font-semibold">完全自主的AI发行大脑</span>，
                        能够从游戏立项开始就全程参与，
                        实现<span class="text-green-400 font-semibold">预测式发行</span>和
                        <span class="text-purple-400 font-semibold">千人千面的个性化体验</span>
                    </p>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                        <div class="bg-blue-500/20 rounded-lg p-4">
                            <i class="fas fa-eye text-blue-400 text-xl mb-2"></i>
                            <div class="font-semibold text-blue-400">智能洞察</div>
                            <div class="text-gray-300">预测市场趋势与用户需求</div>
                        </div>
                        <div class="bg-green-500/20 rounded-lg p-4">
                            <i class="fas fa-magic text-green-400 text-xl mb-2"></i>
                            <div class="font-semibold text-green-400">自主创造</div>
                            <div class="text-gray-300">无限生成优质创意内容</div>
                        </div>
                        <div class="bg-purple-500/20 rounded-lg p-4">
                            <i class="fas fa-heart text-purple-400 text-xl mb-2"></i>
                            <div class="font-semibold text-purple-400">情感连接</div>
                            <div class="text-gray-300">建立玩家深度情感体验</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Bento Grid 总结 -->
    <section id="summary" class="py-0 relative overflow-hidden">
        <div class="text-center mb-8 pt-20">
            <h2 class="text-4xl md:text-5xl font-bold gradient-text">核心亮点总览</h2>
            <p class="text-xl text-gray-300 mt-4">苹果风格的产品特性展示</p>
        </div>
        
        <div class="bento-grid">
            <!-- 核心概念 -->
            <div class="bento-item bento-1">
                <i class="fas fa-robot text-5xl mb-4 text-blue-400"></i>
                <h3 class="text-2xl font-bold mb-4 text-white">AI游戏发行Agent</h3>
                <p class="text-gray-300 text-lg leading-relaxed">
                    基于AI技术的游戏发行解决方案<br>
                    <span class="text-green-400 font-semibold">"更智能的投放，更精准的运营，更高效的转化"</span>
                </p>
                <div class="mt-6 space-y-2">
                    <div class="text-3xl font-bold text-green-400">3倍</div>
                    <div class="text-sm text-gray-400">投放效率提升</div>
                </div>
            </div>

            <!-- 技术优势 -->
            <div class="bento-item bento-2">
                <i class="fas fa-brain text-3xl mb-3 text-cyan-400"></i>
                <h4 class="text-lg font-bold mb-3 text-white">AI技术突破</h4>
                <div class="space-y-2 text-sm">
                    <div class="flex justify-between">
                        <span>素材生成</span>
                        <span class="text-cyan-400">2小时</span>
                    </div>
                    <div class="flex justify-between">
                        <span>预测准确率</span>
                        <span class="text-cyan-400">85%+</span>
                    </div>
                    <div class="flex justify-between">
                        <span>风控识别</span>
                        <span class="text-cyan-400">99%</span>
                    </div>
                </div>
            </div>

            <!-- 市场机会 -->
            <div class="bento-item bento-3">
                <i class="fas fa-chart-line text-4xl mb-4 text-orange-400"></i>
                <h4 class="text-xl font-bold mb-3 text-white">市场机会</h4>
                <div class="text-center">
                    <div class="text-3xl font-bold text-orange-400 mb-2">2000亿</div>
                    <div class="text-sm text-gray-300">2025-2029年AI游戏市场增长</div>
                </div>
                <div class="mt-4 text-sm text-gray-300">
                    2025年AI游戏元年已至
                </div>
            </div>

            <!-- 双引擎架构 -->
            <div class="bento-item bento-4">
                <h4 class="text-lg font-bold mb-4 text-white">双引擎架构</h4>
                <div class="space-y-3">
                    <div class="flex items-center">
                        <i class="fas fa-bullhorn text-blue-400 mr-2"></i>
                        <span class="text-sm">投放Agent</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-users text-purple-400 mr-2"></i>
                        <span class="text-sm">运营Agent</span>
                    </div>
                </div>
                <div class="mt-4 text-xs text-gray-400 text-center">
                    从获客到留存的全流程智能化
                </div>
            </div>

            <!-- 核心功能 -->
            <div class="bento-item bento-5">
                <h4 class="text-xl font-bold mb-4 text-white text-center">核心功能矩阵</h4>
                <div class="grid grid-cols-2 gap-4 text-sm">
                    <div class="text-center">
                        <i class="fas fa-magic text-blue-400 mb-2 block"></i>
                        <div>素材自动生成</div>
                    </div>
                    <div class="text-center">
                        <i class="fas fa-chart-bar text-green-400 mb-2 block"></i>
                        <div>效果预测</div>
                    </div>
                    <div class="text-center">
                        <i class="fas fa-user-chart text-purple-400 mb-2 block"></i>
                        <div>用户分析</div>
                    </div>
                    <div class="text-center">
                        <i class="fas fa-shield-alt text-red-400 mb-2 block"></i>
                        <div>智能风控</div>
                    </div>
                </div>
            </div>

            <!-- 竞争优势 -->
            <div class="bento-item bento-6">
                <i class="fas fa-trophy text-3xl mb-3 text-yellow-400"></i>
                <h4 class="text-lg font-bold mb-3 text-white">竞争优势</h4>
                <div class="space-y-2 text-sm">
                    <div><i class="fas fa-check text-green-400 mr-2"></i>轻量化部署</div>
                    <div><i class="fas fa-check text-green-400 mr-2"></i>中小厂商友好</div>
                    <div><i class="fas fa-check text-green-400 mr-2"></i>标准化产品</div>
                    <div><i class="fas fa-check text-green-400 mr-2"></i>快速复制</div>
                </div>
            </div>

            <!-- 技术演进 -->
            <div class="bento-item bento-7">
                <h5 class="font-bold mb-2 text-white">技术演进路径</h5>
                <div class="text-xs space-y-1">
                    <div>基础构建 → 系统完整 → 智能演进</div>
                    <div class="text-gray-400">核心验证 → 功能完备 → 自主决策</div>
                </div>
            </div>

            <!-- 技术创新 -->
            <div class="bento-item bento-8">
                <i class="fas fa-flask text-2xl mb-2 text-green-400"></i>
                <div class="text-sm font-bold text-white">创新实验</div>
                <div class="text-xs text-gray-300 mt-1">概念验证</div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-slate-900 py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <div class="flex items-center justify-center space-x-2 mb-6">
                    <i class="fas fa-robot text-2xl text-blue-400"></i>
                    <span class="text-xl font-bold text-white">AI游戏发行Agent</span>
                </div>
                <p class="text-gray-400 mb-6">用AI重新定义游戏发行，让成功变得更简单</p>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
                    <div>
                        <h4 class="text-lg font-semibold text-white mb-4">核心技术</h4>
                        <ul class="space-y-2 text-gray-400 text-sm">
                            <li>大模型驱动的内容生成</li>
                            <li>机器学习预测算法</li>
                            <li>实时数据分析引擎</li>
                            <li>多模态AI技术栈</li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="text-lg font-semibold text-white mb-4">应用场景</h4>
                        <ul class="space-y-2 text-gray-400 text-sm">
                            <li>智能素材创作</li>
                            <li>精准投放策略</li>
                            <li>用户行为洞察</li>
                            <li>风险管控系统</li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="text-lg font-semibold text-white mb-4">技术优势</h4>
                        <ul class="space-y-2 text-gray-400 text-sm">
                            <li>端到端自动化</li>
                            <li>实时学习优化</li>
                            <li>跨平台兼容</li>
                            <li>安全可控部署</li>
                        </ul>
                    </div>
                </div>
                
                <div class="border-t border-gray-700 pt-6">
                    <div class="flex flex-col md:flex-row justify-between items-center">
                        <p class="text-gray-400 text-sm mb-4 md:mb-0">
                            &copy; 2025 内部技术分享文档 - AI游戏发行Agent概念展示
                        </p>
                        <div class="flex items-center space-x-4 text-sm text-gray-400">
                            <span><i class="fas fa-flask mr-1"></i>技术研发</span>
                            <span><i class="fas fa-lightbulb mr-1"></i>创新实验</span>
                            <span><i class="fas fa-chart-line mr-1"></i>概念验证</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // 初始化 Mermaid
        mermaid.initialize({ 
            theme: 'dark',
            themeVariables: {
                primaryColor: '#3b82f6',
                primaryTextColor: '#ffffff',
                primaryBorderColor: '#60a5fa',
                lineColor: '#60a5fa',
                secondaryColor: '#8b5cf6',
                tertiaryColor: '#06b6d4',
                background: '#1e293b',
                mainBkg: '#334155',
                secondBkg: '#475569',
                tertiaryBkg: '#64748b'
            }
        });

        // 滚动进度条
        window.addEventListener('scroll', () => {
            const scrolled = (window.scrollY / (document.documentElement.scrollHeight - window.innerHeight)) * 100;
            document.getElementById('scrollProgress').style.width = scrolled + '%';
        });

        // 平滑滚动
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // 导航栏背景变化
        window.addEventListener('scroll', () => {
            const nav = document.querySelector('nav');
            if (window.scrollY > 50) {
                nav.style.background = 'rgba(15, 15, 35, 0.95)';
            } else {
                nav.style.background = 'rgba(15, 15, 35, 0.8)';
            }
        });

        // 卡片hover效果
        document.querySelectorAll('.card-hover').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-8px) scale(1.02)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });

        // 数字动画
        function animateNumber(element, start, end, duration) {
            let startTime = null;
            function animation(currentTime) {
                if (startTime === null) startTime = currentTime;
                const timeElapsed = currentTime - startTime;
                const progress = Math.min(timeElapsed / duration, 1);
                const value = Math.floor(progress * (end - start) + start);
                element.textContent = value + (element.dataset.suffix || '');
                if (progress < 1) {
                    requestAnimationFrame(animation);
                }
            }
            requestAnimationFrame(animation);
        }

        // 观察者API用于触发动画
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate');
                    
                    // 触发数字动画
                    const numbers = entry.target.querySelectorAll('[data-animate-number]');
                    numbers.forEach(number => {
                        const end = parseInt(number.dataset.animateNumber);
                        animateNumber(number, 0, end, 2000);
                    });
                }
            });
        });

        // 观察所有可能需要动画的元素
        document.querySelectorAll('.card-hover, .timeline-item, .bento-item').forEach(el => {
            observer.observe(el);
        });

        // Bento Grid 响应式调整
        function adjustBentoGrid() {
            const grid = document.querySelector('.bento-grid');
            if (window.innerWidth < 768) {
                grid.style.gridTemplateColumns = 'repeat(6, 1fr)';
                grid.style.gridTemplateRows = 'repeat(12, 1fr)';
                
                // 重新分配移动端的网格位置
                document.querySelector('.bento-1').style.gridColumn = '1 / 7';
                document.querySelector('.bento-1').style.gridRow = '1 / 4';
                document.querySelector('.bento-2').style.gridColumn = '1 / 4';
                document.querySelector('.bento-2').style.gridRow = '4 / 6';
                document.querySelector('.bento-3').style.gridColumn = '4 / 7';
                document.querySelector('.bento-3').style.gridRow = '4 / 6';
                // ... 其他元素的移动端布局
            }
        }

        window.addEventListener('resize', adjustBentoGrid);
        adjustBentoGrid();

        // 初始化时的一些动画效果
        window.addEventListener('load', () => {
            document.body.style.opacity = '1';
            
            // 延迟显示某些元素
            setTimeout(() => {
                document.querySelectorAll('.floating').forEach(el => {
                    el.style.opacity = '1';
                });
            }, 500);
        });
    </script>
</body>
</html>