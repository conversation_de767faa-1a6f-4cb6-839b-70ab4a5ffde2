<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI制作专业PPT指南</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-100-M/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            /* 背景层次 */
            --apple-bg-primary: #000000;
            --apple-bg-secondary: #f5f5f7;
            --apple-card-bg: #ffffff;
            
            /* 文字层次 */
            --apple-text-hero: #1d1d1f;
            --apple-text-primary: #1d1d1f;
            --apple-text-secondary: #86868b;
            
            /* 装饰色 */
            --apple-accent-blue: rgba(0, 122, 255, 0.2);
            --apple-accent-green: rgba(52, 199, 89, 0.2);
            --apple-accent-orange: rgba(255, 149, 0, 0.2);
            --apple-accent-purple: rgba(175, 82, 222, 0.2);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: var(--apple-bg-secondary);
            overflow-x: hidden;
            min-height: 100vh;
        }

        .universe-container {
            width: 100vw;
            min-height: 100vh;
            position: relative;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 40px;
        }

        .universe-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            grid-template-rows: repeat(5, minmax(140px, 1fr));
            gap: 20px;
            width: 100%;
            max-width: 1440px;
            height: 100vh;
            min-height: 900px;
        }

        .card {
            background: var(--apple-card-bg);
            border-radius: 24px;
            padding: 40px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            position: relative;
            overflow: hidden;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 40px rgba(0, 0, 0, 0.12);
        }

        /* 中心主卡片 */
        .hero-card {
            grid-column: 3 / 6;
            grid-row: 2 / 4;
            z-index: 10;
            padding: 60px;
            text-align: center;
            align-items: center;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        }

        .hero-label {
            font-size: 14px;
            font-weight: 500;
            color: var(--apple-text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 16px;
        }

        .hero-title {
            font-size: 56px;
            font-weight: 700;
            color: var(--apple-text-hero);
            margin-bottom: 24px;
            line-height: 1.1;
        }

        .hero-subtitle {
            font-size: 21px;
            font-weight: 500;
            color: var(--apple-text-secondary);
            line-height: 1.4;
            max-width: 400px;
        }

        /* 环绕卡片布局 */
        .card-top-left { grid-column: 1 / 3; grid-row: 1 / 2; }
        .card-top { grid-column: 3 / 5; grid-row: 1 / 2; }
        .card-top-right { grid-column: 5 / 8; grid-row: 1 / 2; }
        .card-left { grid-column: 1 / 3; grid-row: 2 / 4; }
        .card-right { grid-column: 6 / 8; grid-row: 2 / 4; }
        .card-bottom-left { grid-column: 1 / 4; grid-row: 4 / 6; }
        .card-bottom-right { grid-column: 4 / 8; grid-row: 4 / 6; }

        /* 数据展示卡片 */
        .data-card {
            justify-content: space-between;
        }

        .data-number {
            font-size: 72px;
            font-weight: 700;
            margin: 0;
            color: var(--apple-text-hero);
            line-height: 1;
        }

        .data-label {
            font-size: 14px;
            font-weight: 500;
            color: var(--apple-text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-top: 12px;
        }

        /* 特性卡片 */
        .feature-card h3 {
            font-size: 32px;
            font-weight: 600;
            color: var(--apple-text-primary);
            margin-bottom: 16px;
        }

        .feature-card p {
            font-size: 17px;
            font-weight: 400;
            color: var(--apple-text-secondary);
            line-height: 1.6;
        }

        /* 流程卡片 */
        .process-card {
            padding: 32px;
        }

        .process-card h4 {
            font-size: 24px;
            font-weight: 600;
            color: var(--apple-text-primary);
            margin-bottom: 12px;
        }

        .process-card p {
            font-size: 15px;
            font-weight: 400;
            color: var(--apple-text-secondary);
            line-height: 1.5;
        }

        /* 图标水印 */
        .icon-watermark {
            position: absolute;
            font-size: 200px;
            opacity: 0.03;
            bottom: -50px;
            right: -50px;
            transform: rotate(-15deg);
        }

        /* 装饰性渐变 */
        .gradient-connector {
            position: absolute;
            width: 600px;
            height: 600px;
            background: radial-gradient(
                circle at center,
                var(--apple-accent-blue),
                transparent 70%
            );
            filter: blur(120px);
            opacity: 0.3;
            pointer-events: none;
            z-index: 1;
        }

        .gradient-connector-2 {
            position: absolute;
            width: 400px;
            height: 400px;
            background: radial-gradient(
                circle at center,
                var(--apple-accent-green),
                transparent 70%
            );
            filter: blur(100px);
            opacity: 0.25;
            pointer-events: none;
            z-index: 1;
            top: 60%;
            right: 10%;
        }

        .gradient-connector-3 {
            position: absolute;
            width: 300px;
            height: 300px;
            background: radial-gradient(
                circle at center,
                var(--apple-accent-orange),
                transparent 70%
            );
            filter: blur(80px);
            opacity: 0.2;
            pointer-events: none;
            z-index: 1;
            bottom: 20%;
            left: 15%;
        }

        /* 响应式 */
        @media (min-width: 1200px) {
            .universe-grid {
                min-height: 900px;
            }
        }

        @media (min-width: 1920px) {
            .universe-container {
                max-width: 1600px;
                margin: 0 auto;
            }
        }

        @media (max-width: 1200px) {
            .universe-grid {
                grid-template-columns: repeat(5, 1fr);
                grid-template-rows: repeat(6, minmax(120px, 1fr));
            }
            
            .hero-card {
                grid-column: 2 / 5;
                grid-row: 2 / 4;
            }
            
            .card-top-left { grid-column: 1 / 3; grid-row: 1 / 2; }
            .card-top { grid-column: 3 / 6; grid-row: 1 / 2; }
            .card-left { grid-column: 1 / 2; grid-row: 2 / 4; }
            .card-right { grid-column: 5 / 6; grid-row: 2 / 4; }
            .card-bottom-left { grid-column: 1 / 3; grid-row: 4 / 6; }
            .card-bottom-right { grid-column: 3 / 6; grid-row: 4 / 6; }
        }
    </style>
</head>
<body>
    <div class="universe-container">
        <!-- 装饰性背景元素 -->
        <div class="gradient-connector" style="top: 20%; left: 30%;"></div>
        <div class="gradient-connector-2"></div>
        <div class="gradient-connector-3"></div>
        
        <div class="universe-grid">
            <!-- 顶部左侧：研究方案选择 -->
            <div class="card process-card card-top-left">
                <i class="fas fa-search icon-watermark"></i>
                <h4>第一步</h4>
                <p>选择研究方案<br>Gemini覆盖面广<br>GPT精确深挖</p>
            </div>
            
            <!-- 顶部中央：时间效率 -->
            <div class="card data-card card-top">
                <p class="data-label">完成时间</p>
                <h2 class="data-number">20<span style="font-size: 36px;">分钟</span></h2>
            </div>
            
            <!-- 顶部右侧：Deep Research优势 -->
            <div class="card feature-card card-top-right">
                <i class="fas fa-brain icon-watermark"></i>
                <h3>Deep Research</h3>
                <p>边思考边搜索，数据完全可信，无AI幻觉</p>
            </div>
            
            <!-- 左侧：Gemini特点 -->
            <div class="card feature-card card-left">
                <i class="fas fa-globe icon-watermark"></i>
                <h3>Gemini 2.5 Pro</h3>
                <p>覆盖面广，适合快速了解问题的方方面面，提供全面的行业概览</p>
            </div>
            
            <!-- 中心英雄卡片 -->
            <div class="card hero-card">
                <p class="hero-label">AI工作流程</p>
                <h1 class="hero-title">专业PPT制作</h1>
                <p class="hero-subtitle">DeepResearch + Gamma<br>快速制作专业演示文稿</p>
            </div>
            
            <!-- 右侧：GPT特点 -->
            <div class="card feature-card card-right">
                <i class="fas fa-bullseye icon-watermark"></i>
                <h3>GPT Research</h3>
                <p>精确深挖，不断细化问题，适合对具体方向进行深度分析</p>
            </div>
            
            <!-- 底部左侧：第二步 -->
            <div class="card process-card card-bottom-left">
                <i class="fas fa-chart-line icon-watermark"></i>
                <h4>第二步：研究结果对比</h4>
                <p>GPT报告精确，准确找到新产品及时间线<br>Gemini报告宽泛，覆盖背景知识和玩家特征<br>两种方案互相补充</p>
            </div>
            
            <!-- 底部右侧：第三步 -->
            <div class="card process-card card-bottom-right">
                <i class="fas fa-magic icon-watermark"></i>
                <h4>第三步：Gamma制作PPT</h4>
                <p>关键技巧：先选择"保留原文"模式自动切割分页，再切换"压缩模式"<br>选择GPT Image模型生成高质量图片<br>自动压缩内容、排版、生成相关图片</p>
            </div>
        </div>
    </div>
</body>
</html>