<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI深度研究 × Gamma - 专业PPT制作新范式</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            color: #1d1d1f;
            line-height: 1.5;
            overflow-x: hidden;
        }

        /* 第一幕：震撼开场 */
        .hero-section {
            height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            position: relative;
            background: radial-gradient(ellipse at center, rgba(0,125,255,0.05) 0%, transparent 70%);
        }

        .hero-title {
            font-size: clamp(4rem, 8vw, 8rem);
            font-weight: 700;
            letter-spacing: -0.02em;
            margin-bottom: 2rem;
            background: linear-gradient(135deg, #1d1d1f 0%, #007AFF 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .hero-subtitle {
            font-size: clamp(1.2rem, 3vw, 2rem);
            color: #86868b;
            font-weight: 400;
            max-width: 800px;
            margin-bottom: 3rem;
        }

        .hero-stats {
            display: flex;
            gap: 4rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 3rem;
            font-weight: 700;
            color: #007AFF;
            display: block;
        }

        .stat-label {
            font-size: 1rem;
            color: #86868b;
            margin-top: 0.5rem;
        }

        /* 第二幕：动态网格内容 */
        .content-section {
            max-width: 1400px;
            margin: 0 auto;
            padding: 8rem 2rem;
        }

        .grid-container {
            display: grid;
            grid-template-columns: repeat(12, 1fr);
            gap: 2rem;
            margin-bottom: 4rem;
        }

        .content-card {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 2.5rem;
            box-shadow: 0 4px 24px rgba(0, 0, 0, 0.08);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .content-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
        }

        /* 英雄组合布局 */
        .hero-combo .main-card {
            grid-column: span 8;
        }

        .hero-combo .side-cards {
            grid-column: span 4;
            display: grid;
            gap: 2rem;
        }

        /* L型布局 */
        .l-layout .corner-card {
            grid-column: span 7;
            grid-row: span 2;
        }

        .l-layout .small-card-1 {
            grid-column: span 5;
        }

        .l-layout .small-card-2 {
            grid-column: span 5;
        }

        /* 三等分布局 */
        .three-equal .card {
            grid-column: span 4;
        }

        /* 瀑布流布局 */
        .waterfall .tall-card {
            grid-column: span 6;
            grid-row: span 2;
        }

        .waterfall .wide-card {
            grid-column: span 6;
        }

        .waterfall .small-cards {
            grid-column: span 6;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
        }

        /* 马赛克布局 */
        .mosaic .large {
            grid-column: span 6;
            grid-row: span 2;
        }

        .mosaic .medium {
            grid-column: span 3;
        }

        .mosaic .small {
            grid-column: span 3;
        }

        .card-title {
            font-size: clamp(1.5rem, 3vw, 2.5rem);
            font-weight: 700;
            margin-bottom: 1.5rem;
            color: #1d1d1f;
        }

        .card-subtitle {
            font-size: 1.2rem;
            color: #007AFF;
            margin-bottom: 1rem;
            font-weight: 600;
        }

        .card-description {
            font-size: 1.1rem;
            color: #515154;
            line-height: 1.6;
            margin-bottom: 1.5rem;
        }

        .feature-list {
            list-style: none;
        }

        .feature-list li {
            position: relative;
            padding-left: 1.5rem;
            margin-bottom: 0.8rem;
            color: #515154;
        }

        .feature-list li::before {
            content: '•';
            color: #007AFF;
            font-weight: bold;
            position: absolute;
            left: 0;
        }

        .step-number {
            display: inline-block;
            width: 3rem;
            height: 3rem;
            background: linear-gradient(135deg, #007AFF, #00C896);
            color: white;
            border-radius: 50%;
            text-align: center;
            line-height: 3rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }

        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-top: 2rem;
        }

        .vs-item {
            padding: 1.5rem;
            background: rgba(0, 122, 255, 0.05);
            border-radius: 16px;
            border-left: 4px solid #007AFF;
        }

        .efficiency-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }

        .efficiency-item {
            text-align: center;
            padding: 1.5rem;
            background: rgba(0, 200, 150, 0.05);
            border-radius: 16px;
        }

        .efficiency-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: #00C896;
            display: block;
        }

        /* 第三幕：网格总结 */
        .summary-section {
            height: 100vh;
            display: grid;
            grid-template-columns: repeat(12, 1fr);
            grid-template-rows: repeat(8, 1fr);
            gap: 1.5rem;
            padding: 2rem;
            max-width: 1400px;
            margin: 0 auto;
        }

        .center-card {
            grid-column: 5 / 9;
            grid-row: 3 / 7;
            background: linear-gradient(135deg, #007AFF, #00C896);
            color: white;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
        }

        .center-title {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }

        .center-subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        /* 环绕卡片 */
        .surrounding-card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 1.5rem;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            box-shadow: 0 4px 24px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
        }

        .surrounding-card:hover {
            transform: scale(1.05);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
        }

        .surround-1 { grid-column: 2 / 4; grid-row: 1 / 3; }
        .surround-2 { grid-column: 5 / 8; grid-row: 1 / 2; }
        .surround-3 { grid-column: 9 / 12; grid-row: 1 / 3; }
        .surround-4 { grid-column: 10 / 13; grid-row: 4 / 6; }
        .surround-5 { grid-column: 9 / 12; grid-row: 7 / 9; }
        .surround-6 { grid-column: 5 / 8; grid-row: 8 / 9; }
        .surround-7 { grid-column: 2 / 4; grid-row: 7 / 9; }
        .surround-8 { grid-column: 1 / 2; grid-row: 4 / 6; }

        .surround-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .surround-title {
            font-size: 1rem;
            font-weight: 600;
            color: #1d1d1f;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .hero-stats {
                gap: 2rem;
            }

            .stat-number {
                font-size: 2rem;
            }

            .content-section {
                padding: 4rem 1rem;
            }

            .grid-container {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }

            .hero-combo .main-card,
            .hero-combo .side-cards,
            .l-layout .corner-card,
            .l-layout .small-card-1,
            .l-layout .small-card-2,
            .three-equal .card,
            .waterfall .tall-card,
            .waterfall .wide-card,
            .waterfall .small-cards,
            .mosaic .large,
            .mosaic .medium,
            .mosaic .small {
                grid-column: 1;
            }

            .comparison-grid {
                grid-template-columns: 1fr;
            }

            .summary-section {
                grid-template-columns: 1fr;
                grid-template-rows: auto;
                height: auto;
                gap: 1rem;
            }

            .center-card,
            .surrounding-card {
                grid-column: 1;
            }

            .center-title {
                font-size: 2rem;
            }
        }

        /* 平滑滚动 */
        html {
            scroll-behavior: smooth;
        }

        /* 内容淡入动画 */
        .content-card {
            opacity: 0;
            transform: translateY(20px);
            animation: fadeInUp 0.6s ease forwards;
        }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 错开动画时间 */
        .content-card:nth-child(1) { animation-delay: 0.1s; }
        .content-card:nth-child(2) { animation-delay: 0.2s; }
        .content-card:nth-child(3) { animation-delay: 0.3s; }
        .content-card:nth-child(4) { animation-delay: 0.4s; }
        .content-card:nth-child(5) { animation-delay: 0.5s; }
    </style>
</head>
<body>
    <!-- 第一幕：震撼开场 -->
    <section class="hero-section">
        <h1 class="hero-title">AI深度研究 × Gamma</h1>
        <p class="hero-subtitle">20分钟完成从行业调研到PPT输出的全过程<br>效率提升40-60倍的专业工作流程</p>
        <div class="hero-stats">
            <div class="stat-item">
                <span class="stat-number">20</span>
                <span class="stat-label">分钟完成全流程</span>
            </div>
            <div class="stat-item">
                <span class="stat-number">60×</span>
                <span class="stat-label">效率提升倍数</span>
            </div>
            <div class="stat-item">
                <span class="stat-number">100%</span>
                <span class="stat-label">数据可信度</span>
            </div>
        </div>
    </section>

    <!-- 第二幕：动态网格内容 -->
    <section class="content-section">
        <!-- 英雄组合：核心方案对比 -->
        <div class="grid-container hero-combo">
            <div class="content-card main-card">
                <h2 class="card-title">选择最适合的研究方案</h2>
                <div class="comparison-grid">
                    <div class="vs-item">
                        <h3 class="card-subtitle">Gemini Deep Research</h3>
                        <p class="card-description">广度优先的全景分析，覆盖面广，适合快速了解问题的方方面面</p>
                        <ul class="feature-list">
                            <li>自动浏览数百个网站</li>
                            <li>交叉验证信息准确性</li>
                            <li>多维度行业概览</li>
                            <li>深厚搜索领域积累</li>
                        </ul>
                    </div>
                    <div class="vs-item">
                        <h3 class="card-subtitle">ChatGPT Deep Research</h3>
                        <p class="card-description">精度优先的深度挖掘，交互式问题细化，确保研究方向精准匹配</p>
                        <ul class="feature-list">
                            <li>交互式问题细化</li>
                            <li>精确深挖特定方向</li>
                            <li>准确识别新产品</li>
                            <li>详细数据源追溯</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="side-cards">
                <div class="content-card">
                    <h3 class="card-subtitle">技术优势</h3>
                    <p class="card-description">边思考边搜索，迭代式研究，确保搜索质量和推理能力</p>
                </div>
                <div class="content-card">
                    <h3 class="card-subtitle">选择策略</h3>
                    <p class="card-description">全面了解选Gemini，深度分析选GPT，或组合使用</p>
                </div>
            </div>
        </div>

        <!-- L型布局：流程步骤 -->
        <div class="grid-container l-layout">
            <div class="content-card corner-card">
                <h2 class="card-title">Gamma制作PPT完整流程</h2>
                <p class="card-description">业界领先的AI演示文稿制作工具，基于先进AI内容生成技术</p>
                
                <div style="margin-top: 2rem;">
                    <div style="margin-bottom: 2rem;">
                        <span class="step-number">1</span>
                        <h4 style="display: inline-block; margin-left: 1rem; font-size: 1.2rem;">导入内容</h4>
                        <p style="margin-left: 5rem; color: #515154;">复制研究报告，系统自动分析内容结构</p>
                    </div>
                    
                    <div style="margin-bottom: 2rem;">
                        <span class="step-number">2</span>
                        <h4 style="display: inline-block; margin-left: 1rem; font-size: 1.2rem;">内容处理</h4>
                        <p style="margin-left: 5rem; color: #515154;">先保留原文模式，再切换压缩模式优化结构</p>
                    </div>
                    
                    <div style="margin-bottom: 2rem;">
                        <span class="step-number">3</span>
                        <h4 style="display: inline-block; margin-left: 1rem; font-size: 1.2rem;">图像生成</h4>
                        <p style="margin-left: 5rem; color: #515154;">选择GPT Image模型，指令遵循能力最强</p>
                    </div>
                    
                    <div>
                        <span class="step-number">4</span>
                        <h4 style="display: inline-block; margin-left: 1rem; font-size: 1.2rem;">模板选择</h4>
                        <p style="margin-left: 5rem; color: #515154;">商业分析、科技创新、投资报告等专业模板</p>
                    </div>
                </div>
            </div>
            
            <div class="content-card small-card-1">
                <h3 class="card-subtitle">自动化生成</h3>
                <p class="card-description">智能内容压缩、自动版式设计、图片高度相关、品牌一致性</p>
            </div>
            
            <div class="content-card small-card-2">
                <h3 class="card-subtitle">质量保障</h3>
                <p class="card-description">多源验证、专业设计标准、一致性保证</p>
            </div>
        </div>

        <!-- 三等分：核心优势 -->
        <div class="grid-container three-equal">
            <div class="content-card card">
                <h3 class="card-subtitle">信息获取效率</h3>
                <p class="card-description">Deep Research自动化信息搜索、筛选、整理，质量更高</p>
            </div>
            <div class="content-card card">
                <h3 class="card-subtitle">内容创作效率</h3>
                <p class="card-description">Gamma消除传统PPT制作中最耗时的设计和排版环节</p>
            </div>
            <div class="content-card card">
                <h3 class="card-subtitle">迭代优化效率</h3>
                <p class="card-description">支持快速调整和多版本试验，实现快速迭代</p>
            </div>
        </div>

        <!-- 瀑布流：应用场景与成本效益 -->
        <div class="grid-container waterfall">
            <div class="content-card tall-card">
                <h2 class="card-title">广泛应用场景</h2>
                <ul class="feature-list">
                    <li>消费品市场研究</li>
                    <li>技术趋势分析</li>
                    <li>投资机会评估</li>
                    <li>竞争对手调研</li>
                    <li>产品发布策略</li>
                    <li>游戏行业分析</li>
                </ul>
            </div>
            
            <div class="content-card wide-card">
                <h3 class="card-subtitle">成本效益分析</h3>
                <div class="efficiency-stats">
                    <div class="efficiency-item">
                        <span class="efficiency-number">18-28</span>
                        <span class="stat-label">传统方法（小时）</span>
                    </div>
                    <div class="efficiency-item">
                        <span class="efficiency-number">15-28</span>
                        <span class="stat-label">AI方法（分钟）</span>
                    </div>
                </div>
            </div>
            
            <div class="content-card small-cards">
                <div style="background: rgba(0,122,255,0.05); padding: 1.5rem; border-radius: 16px;">
                    <h4 style="color: #007AFF; margin-bottom: 0.5rem;">信息全面性</h4>
                    <p style="font-size: 0.9rem; color: #515154;">覆盖更广泛信息源</p>
                </div>
                <div style="background: rgba(0,200,150,0.05); padding: 1.5rem; border-radius: 16px;">
                    <h4 style="color: #00C896; margin-bottom: 0.5rem;">数据准确性</h4>
                    <p style="font-size: 0.9rem; color: #515154;">多源验证减少错误</p>
                </div>
            </div>
        </div>

        <!-- 马赛克：方法论价值 -->
        <div class="grid-container mosaic">
            <div class="content-card large">
                <h2 class="card-title">方法论核心价值</h2>
                <p class="card-description">通过Deep Research增强输入信息的质量和广度，再用Gamma快速生成结构化强、图片质量高的PPT。这种方法论可以应用到各种知识工作场景中，从根本上改变我们处理信息和创建内容的方式。</p>
                <p style="margin-top: 1.5rem; padding: 1.5rem; background: rgba(0,122,255,0.05); border-radius: 16px; color: #007AFF; font-weight: 600;">在AI时代，掌握正确的工具使用方法比拥有工具本身更重要</p>
            </div>
            
            <div class="content-card medium">
                <h3 class="card-subtitle">持续优化</h3>
                <p class="card-description">随着AI技术发展，定期关注新工具和新功能，持续优化工作流程</p>
            </div>
            
            <div class="content-card medium">
                <h3 class="card-subtitle">竞争优势</h3>
                <p class="card-description">高效完成高质量的研究和演示任务，保持领先地位</p>
            </div>
            
            <div class="content-card small">
                <h4 style="color: #007AFF; margin-bottom: 1rem;">质量提升</h4>
                <p style="font-size: 0.9rem;">设计专业性保证</p>
            </div>
            
            <div class="content-card small">
                <h4 style="color: #00C896; margin-bottom: 1rem;">一致性</h4>
                <p style="font-size: 0.9rem;">避免人工不一致</p>
            </div>
        </div>
    </section>

    <!-- 第三幕：网格总结 -->
    <section class="summary-section">
        <div class="content-card center-card">
            <h2 class="center-title">AI × 设计</h2>
            <p class="center-subtitle">重新定义专业内容创作</p>
        </div>
        
        <div class="surrounding-card surround-1">
            <div class="surround-icon">🧠</div>
            <div class="surround-title">深度研究</div>
        </div>
        
        <div class="surrounding-card surround-2">
            <div class="surround-icon">⚡</div>
            <div class="surround-title">极速生成</div>
        </div>
        
        <div class="surrounding-card surround-3">
            <div class="surround-icon">🎨</div>
            <div class="surround-title">专业设计</div>
        </div>
        
        <div class="surrounding-card surround-4">
            <div class="surround-icon">📊</div>
            <div class="surround-title">数据可视化</div>
        </div>
        
        <div class="surrounding-card surround-5">
            <div class="surround-icon">🔄</div>
            <div class="surround-title">快速迭代</div>
        </div>
        
        <div class="surrounding-card surround-6">
            <div class="surround-icon">🎯</div>
            <div class="surround-title">精准匹配</div>
        </div>
        
        <div class="surrounding-card surround-7">
            <div class="surround-icon">📈</div>
            <div class="surround-title">效率提升</div>
        </div>
        
        <div class="surrounding-card surround-8">
            <div class="surround-icon">🏆</div>
            <div class="surround-title">品质保证</div>
        </div>
    </section>

    <script>
        // 平滑滚动和视差效果
        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            const heroSection = document.querySelector('.hero-section');
            heroSection.style.transform = `translateY(${scrolled * 0.5}px)`;
        });

        // 内容卡片进入视窗时的动画
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.animationPlayState = 'running';
                }
            });
        }, observerOptions);

        document.querySelectorAll('.content-card').forEach(card => {
            card.style.animationPlayState = 'paused';
            observer.observe(card);
        });
    </script>
</body>
</html> 