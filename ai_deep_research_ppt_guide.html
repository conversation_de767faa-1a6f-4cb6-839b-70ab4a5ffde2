<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI深度研究 × Gamma - 20分钟制作专业PPT完整指南</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-100-M/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            /* 背景层次 */
            --apple-bg-primary: #ffffff;
            --apple-bg-secondary: #f5f5f7;
            --apple-card-bg: #ffffff;
            
            /* 文字层次 */
            --apple-text-hero: #1d1d1f;
            --apple-text-primary: #1d1d1f;
            --apple-text-secondary: #86868b;
            
            /* 品牌色（仅20-30%透明度用于装饰） */
            --apple-blue: rgba(0, 122, 255, 0.2);
            --apple-green: rgba(52, 199, 89, 0.2);
            --apple-orange: rgba(255, 149, 0, 0.2);
            --apple-purple: rgba(175, 82, 222, 0.2);
            --apple-pink: rgba(255, 45, 146, 0.2);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, SF Pro Display, Inter, PingFang SC, sans-serif;
            line-height: 1.6;
            color: var(--apple-text-primary);
            background: var(--apple-bg-primary);
            scroll-behavior: smooth;
        }

        .page-container {
            width: 100%;
            overflow-x: hidden;
        }

        /* 英雄开场 */
        .hero-section {
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            background: linear-gradient(135deg, var(--apple-bg-secondary) 0%, var(--apple-bg-primary) 100%);
            overflow: hidden;
        }

        .hero-content {
            text-align: center;
            max-width: 1000px;
            z-index: 2;
            padding: 0 40px;
        }

        .hero-title {
            font-size: 96px;
            font-weight: 700;
            line-height: 1.05;
            letter-spacing: -0.02em;
            margin-bottom: 24px;
            background: linear-gradient(135deg, var(--apple-text-hero) 0%, var(--apple-text-secondary) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .hero-subtitle {
            font-size: 32px;
            font-weight: 500;
            color: var(--apple-text-secondary);
            margin-bottom: 48px;
        }

        .hero-description {
            font-size: 24px;
            font-weight: 400;
            color: var(--apple-text-primary);
            line-height: 1.4;
            max-width: 800px;
            margin: 0 auto;
        }

        .gradient-orb {
            position: absolute;
            border-radius: 50%;
            filter: blur(100px);
            opacity: 0.3;
            pointer-events: none;
        }

        /* 章节样式 */
        .chapter {
            margin-bottom: 0;
        }

        .chapter-hero {
            height: 60vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #000000;
            color: #ffffff;
            position: relative;
            overflow: hidden;
        }

        .chapter-number {
            font-size: 200px;
            font-weight: 800;
            opacity: 0.1;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .chapter-title {
            font-size: 72px;
            font-weight: 700;
            z-index: 1;
            position: relative;
            text-align: center;
            max-width: 800px;
        }

        .chapter-content {
            padding: 120px 60px;
            max-width: 1440px;
            margin: 0 auto;
        }

        .bento-grid {
            display: grid;
            grid-template-columns: repeat(12, 1fr);
            gap: 24px;
            margin-bottom: 120px;
        }

        .card {
            background: var(--apple-card-bg);
            border-radius: 24px;
            padding: 48px;
            box-shadow: 0 4px 24px rgba(0, 0, 0, 0.08);
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-8px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
        }

        .card-large { grid-column: span 8; min-height: 400px; }
        .card-medium { grid-column: span 6; min-height: 300px; }
        .card-small { grid-column: span 4; min-height: 240px; }
        .card-wide { grid-column: span 12; min-height: 200px; }

        .data-card {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .data-value {
            font-size: 84px;
            font-weight: 700;
            line-height: 1;
            margin: 24px 0;
            color: var(--apple-text-hero);
        }

        .data-label {
            font-size: 14px;
            font-weight: 500;
            color: var(--apple-text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .data-description {
            font-size: 17px;
            color: var(--apple-text-primary);
            margin-top: 16px;
        }

        .feature-card {
            background: linear-gradient(135deg, var(--apple-card-bg) 0%, var(--apple-bg-secondary) 100%);
        }

        .feature-icon {
            font-size: 48px;
            margin-bottom: 24px;
            opacity: 0.3;
        }

        .feature-title {
            font-size: 32px;
            font-weight: 600;
            margin-bottom: 16px;
            color: var(--apple-text-hero);
        }

        .feature-description {
            font-size: 17px;
            color: var(--apple-text-primary);
            line-height: 1.6;
        }

        .feature-list {
            list-style: none;
            margin-top: 24px;
        }

        .feature-list li {
            font-size: 17px;
            color: var(--apple-text-primary);
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
        }

        .feature-list li::before {
            content: "✓";
            position: absolute;
            left: 0;
            color: var(--apple-text-secondary);
        }

        /* 中心辐射总结 */
        .finale-section {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 80px;
            background: var(--apple-bg-secondary);
            position: relative;
        }

        .radial-container {
            width: 1200px;
            height: 800px;
            position: relative;
            margin: 0 auto;
        }

        .center-card {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 400px;
            z-index: 10;
            background: var(--apple-card-bg);
            border-radius: 32px;
            padding: 60px;
            text-align: center;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
        }

        .center-card h2 {
            font-size: 48px;
            font-weight: 700;
            margin-bottom: 16px;
            color: var(--apple-text-hero);
        }

        .center-card p {
            font-size: 24px;
            color: var(--apple-text-secondary);
            line-height: 1.4;
        }

        .orbit-card {
            position: absolute;
            width: 280px;
            background: var(--apple-card-bg);
            border-radius: 24px;
            padding: 40px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
        }

        .orbit-card:hover {
            transform: scale(1.05) translateY(-4px);
            box-shadow: 0 16px 48px rgba(0, 0, 0, 0.12);
        }

        .orbit-card h3 {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 12px;
            color: var(--apple-text-hero);
        }

        .orbit-card p {
            font-size: 17px;
            color: var(--apple-text-primary);
            line-height: 1.5;
        }

        .orbit-top { top: 0; left: 50%; transform: translateX(-50%); }
        .orbit-top-right { top: 10%; right: 15%; }
        .orbit-right { top: 50%; right: 0; transform: translateY(-50%); }
        .orbit-bottom-right { bottom: 10%; right: 15%; }
        .orbit-bottom { bottom: 0; left: 50%; transform: translateX(-50%); }
        .orbit-bottom-left { bottom: 10%; left: 15%; }
        .orbit-left { top: 50%; left: 0; transform: translateY(-50%); }
        .orbit-top-left { top: 10%; left: 15%; }

        .pulse-bg {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 600px;
            height: 600px;
            background: radial-gradient(circle, var(--apple-blue), transparent);
            border-radius: 50%;
            animation: pulse 4s ease-in-out infinite;
        }

        @keyframes pulse {
            0% { opacity: 0.3; transform: translate(-50%, -50%) scale(0.8); }
            50% { opacity: 0.15; transform: translate(-50%, -50%) scale(1.2); }
            100% { opacity: 0.3; transform: translate(-50%, -50%) scale(0.8); }
        }

        /* 动效系统 */
        .fade-in {
            opacity: 0;
            transform: translateY(40px);
            transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .fade-in.visible {
            opacity: 1;
            transform: translateY(0);
        }

        .delay-1 { transition-delay: 0.1s; }
        .delay-2 { transition-delay: 0.2s; }
        .delay-3 { transition-delay: 0.3s; }

        /* 响应式设计 */
        @media (max-width: 1024px) {
            .hero-title { font-size: 64px; }
            .hero-subtitle { font-size: 24px; }
            .chapter-title { font-size: 48px; }
            .chapter-number { font-size: 120px; }
            .bento-grid { grid-template-columns: repeat(6, 1fr); }
            .card-large { grid-column: span 6; }
            .card-medium { grid-column: span 6; }
            .card-small { grid-column: span 6; }
            .radial-container { width: 800px; height: 600px; }
            .orbit-card { width: 200px; padding: 20px; }
        }
    </style>
</head>
<body>
    <div class="page-container">
        <!-- 第一段：英雄开场 -->
        <section class="hero-section">
            <div class="hero-content fade-in">
                <h1 class="hero-title">AI深度研究 × Gamma</h1>
                <p class="hero-subtitle">20分钟制作专业PPT完整指南</p>
                <p class="hero-description">从行业调研到PPT输出的全流程，效率提升10倍以上的革命性工作方法</p>
            </div>
            <div class="gradient-orb" style="width: 600px; height: 600px; background: radial-gradient(circle, var(--apple-blue), transparent); top: -200px; right: -200px;"></div>
            <div class="gradient-orb" style="width: 400px; height: 400px; background: radial-gradient(circle, var(--apple-purple), transparent); bottom: -100px; left: -100px;"></div>
        </section>

        <!-- 第二段：垂直叙事 -->
        
        <!-- 章节1：工具选择 -->
        <section class="chapter">
            <div class="chapter-hero">
                <span class="chapter-number">01</span>
                <h2 class="chapter-title">选择最适合的研究方案</h2>
            </div>
            <div class="chapter-content">
                <div class="bento-grid">
                    <!-- 主要对比卡片 -->
                    <div class="card card-large feature-card fade-in">
                        <div class="feature-icon">🔍</div>
                        <h3 class="feature-title">Gemini Deep Research</h3>
                        <p class="feature-description">Google推出的广度优先全景分析工具，采用Gemini 2.5 Pro模型驱动</p>
                        <ul class="feature-list">
                            <li>覆盖面广，多维度分析</li>
                            <li>自动浏览数百个网站</li>
                            <li>交叉验证信息准确性</li>
                            <li>适合建立完整知识框架</li>
                        </ul>
                    </div>
                    
                    <!-- GPT对比 -->
                    <div class="card card-small data-card fade-in delay-1">
                        <div class="data-label">研究工具</div>
                        <div class="data-value">2</div>
                        <p class="data-description">主流AI深度研究平台</p>
                    </div>
                    
                    <div class="card card-medium feature-card fade-in delay-2">
                        <div class="feature-icon">🎯</div>
                        <h3 class="feature-title">ChatGPT Deep Research</h3>
                        <p class="feature-description">精度优先的深度挖掘，交互式问题细化，确保研究方向精准匹配需求</p>
                        <ul class="feature-list">
                            <li>交互式问题细化</li>
                            <li>精确深挖具体方向</li>
                            <li>高质量数据源追溯</li>
                        </ul>
                    </div>
                    
                    <!-- 选择策略 -->
                    <div class="card card-wide feature-card fade-in delay-3">
                        <h3 class="feature-title">选择策略建议</h3>
                        <p class="feature-description">根据需求匹配工具：想要全面了解选Gemini，想要深度分析选GPT。两种工具也可组合使用：先用Gemini建立全景认知，再用GPT深挖关键问题。</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- 章节2：研究对比 -->
        <section class="chapter">
            <div class="chapter-hero">
                <span class="chapter-number">02</span>
                <h2 class="chapter-title">研究结果对比与质量评估</h2>
            </div>
            <div class="chapter-content">
                <div class="bento-grid">
                    <!-- GPT特点 -->
                    <div class="card card-medium feature-card fade-in">
                        <div class="feature-icon">📊</div>
                        <h3 class="feature-title">GPT研究结果特点</h3>
                        <p class="feature-description">精确度极高，准确识别具体产品信息</p>
                        <ul class="feature-list">
                            <li>产品细节精准（开发商、发行商、时间）</li>
                            <li>数据可信度高，有明确来源</li>
                            <li>关键KPI数据准确</li>
                        </ul>
                    </div>
                    
                    <!-- Gemini特点 -->
                    <div class="card card-medium feature-card fade-in delay-1">
                        <div class="feature-icon">🌐</div>
                        <h3 class="feature-title">Gemini研究结果特点</h3>
                        <p class="feature-description">宽泛全面，深入行业背景知识</p>
                        <ul class="feature-list">
                            <li>行业历史演进分析</li>
                            <li>文化偏好差异研究</li>
                            <li>厂商战略布局全面</li>
                        </ul>
                    </div>
                    
                    <!-- 技术优势 -->
                    <div class="card card-large data-card fade-in delay-2">
                        <div class="data-label">技术核心</div>
                        <div class="data-value">边思考<br/>边搜索</div>
                        <p class="data-description">Deep Research的最大优势是迭代式研究方法，边思考边搜索新内容，确保搜索质量极高、数据完全可信、推理能力强大</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- 章节3：Gamma制作 -->
        <section class="chapter">
            <div class="chapter-hero">
                <span class="chapter-number">03</span>
                <h2 class="chapter-title">Gamma制作PPT的完整流程</h2>
            </div>
            <div class="chapter-content">
                <div class="bento-grid">
                    <!-- 平台介绍 -->
                    <div class="card card-wide feature-card fade-in">
                        <div class="feature-icon">🎨</div>
                        <h3 class="feature-title">Gamma平台核心优势</h3>
                        <p class="feature-description">2025年最领先的AI演示文稿制作工具，基于先进的AI内容生成技术，支持多种输出格式，内置高质量图像生成功能，智能版式设计和品牌一致性保证</p>
                    </div>
                    
                    <!-- 操作步骤 -->
                    <div class="card card-large feature-card fade-in delay-1">
                        <h3 class="feature-title">详细操作步骤</h3>
                        <ul class="feature-list">
                            <li><strong>导入内容</strong> - 选择"粘贴文本"，复制研究报告</li>
                            <li><strong>内容处理</strong> - 先选择"保留原文"模式，再切换"压缩模式"</li>
                            <li><strong>图像生成</strong> - 建议选择GPT Image模型，指令遵循能力最强</li>
                            <li><strong>模板选择</strong> - 商业分析、科技创新、投资报告等专业模板</li>
                        </ul>
                    </div>
                    
                    <!-- 效率数据 -->
                    <div class="card card-small data-card fade-in delay-2">
                        <div class="data-label">制作时间</div>
                        <div class="data-value">20<span style="font-size: 32px;">分钟</span></div>
                        <p class="data-description">从研究到PPT完成</p>
                    </div>
                    
                    <!-- 自动化特性 */
                    <div class="card card-medium feature-card fade-in delay-3">
                        <div class="feature-icon">⚡</div>
                        <h3 class="feature-title">自动化生成优势</h3>
                        <ul class="feature-list">
                            <li>智能内容压缩</li>
                            <li>自动版式设计</li>
                            <li>图片高度相关</li>
                            <li>品牌一致性保证</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- 章节4：效率分析 -->
        <section class="chapter">
            <div class="chapter-hero">
                <span class="chapter-number">04</span>
                <h2 class="chapter-title">效率革命与成本分析</h2>
            </div>
            <div class="chapter-content">
                <div class="bento-grid">
                    <!-- 传统方法时间 -->
                    <div class="card card-medium data-card fade-in">
                        <div class="data-label">传统方法总时间</div>
                        <div class="data-value">18-28<span style="font-size: 32px;">小时</span></div>
                        <p class="data-description">信息收集8-12h + 资料整理4-6h + PPT制作6-10h</p>
                    </div>
                    
                    <!-- AI方法时间 -->
                    <div class="card card-medium data-card fade-in delay-1">
                        <div class="data-label">AI辅助方法总时间</div>
                        <div class="data-value">15-28<span style="font-size: 32px;">分钟</span></div>
                        <p class="data-description">Deep Research 5-10分钟 + 整理5-10分钟 + Gamma生成5-8分钟</p>
                    </div>
                    
                    <!-- 效率提升 */
                    <div class="card card-small data-card fade-in delay-2">
                        <div class="data-label">效率提升</div>
                        <div class="data-value">40-60<span style="font-size: 32px;">倍</span></div>
                        <p class="data-description">革命性的工作效率改进</p>
                    </div>
                    
                    <!-- 质量提升 */
                    <div class="card card-large feature-card fade-in delay-3">
                        <div class="feature-icon">📈</div>
                        <h3 class="feature-title">质量提升维度</h3>
                        <ul class="feature-list">
                            <li><strong>信息全面性</strong> - AI覆盖更广泛的信息源</li>
                            <li><strong>数据准确性</strong> - 多源验证减少错误</li>
                            <li><strong>设计专业性</strong> - AI模板保证视觉质量</li>
                            <li><strong>一致性保证</strong> - 避免人工操作的不一致</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- 第三段：中心辐射总结 -->
        <section class="finale-section">
            <div class="radial-container">
                <div class="pulse-bg"></div>
                
                <!-- 中心主卡片 -->
                <div class="center-card fade-in">
                    <h2>AI时代的<br/>工作流程</h2>
                    <p>掌握正确的工具使用方法<br/>比拥有工具本身更重要</p>
                </div>
                
                <!-- 8个环绕卡片 -->
                <div class="orbit-card orbit-top fade-in delay-1">
                    <h3>Deep Research</h3>
                    <p>AI驱动的智能研究，多源验证，迭代优化</p>
                </div>
                
                <div class="orbit-card orbit-top-right fade-in delay-2">
                    <h3>Gamma平台</h3>
                    <p>领先的AI演示制作工具，自动化设计与生成</p>
                </div>
                
                <div class="orbit-card orbit-right fade-in delay-3">
                    <h3>效率提升</h3>
                    <p>40-60倍的工作效率改进，质量显著提升</p>
                </div>
                
                <div class="orbit-card orbit-bottom-right fade-in delay-1">
                    <h3>质量保障</h3>
                    <p>多维度质量提升，专业设计标准，一致性保证</p>
                </div>
                
                <div class="orbit-card orbit-bottom fade-in delay-2">
                    <h3>应用场景</h3>
                    <p>市场研究、趋势分析、投资评估、竞争调研</p>
                </div>
                
                <div class="orbit-card orbit-bottom-left fade-in delay-3">
                    <h3>持续优化</h3>
                    <p>关注新工具和功能，不断优化工作流程</p>
                </div>
                
                <div class="orbit-card orbit-left fade-in delay-1">
                    <h3>方法论意义</h3>
                    <p>从根本上改变信息处理和内容创建方式</p>
                </div>
                
                <div class="orbit-card orbit-top-left fade-in delay-2">
                    <h3>竞争优势</h3>
                    <p>在快节奏环境中，高效完成高质量任务</p>
                </div>
            </div>
        </section>
    </div>

    <script>
        // 滚动触发动画
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                }
            });
        }, { 
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        });

        // 观察所有需要动画的元素
        document.querySelectorAll('.fade-in').forEach(el => {
            observer.observe(el);
        });

        // 平滑滚动增强
        document.addEventListener('DOMContentLoaded', function() {
            // 为页面添加一些交互增强
            const cards = document.querySelectorAll('.card');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-8px) scale(1.02)';
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });
        });
    </script>
</body>
</html> 