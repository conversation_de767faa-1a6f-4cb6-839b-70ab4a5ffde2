---
description: 
globs: 
alwaysApply: false
---
---
description: 苹果设计语言HTML页面开发规范
globs: ["*.html", "*.css", "*.js"]
alwaysApply: true
---

# 苹果设计语言HTML页面开发规范

## 设计哲学
- **渐进式揭示，爆炸式总结** - 像讲故事一样逐步展开内容，最后以全景视角震撼收尾
- **克制使用色彩，让数据说话，用留白创造高级感**
- **像设计宇宙星系一样设计页面** - 核心恒星（主题）居中，行星（特性）环绕，创造引力和平衡
- **像拍电影一样设计页面** - 开场吸引，逐步深入，高潮迭起，完美收尾

## 页面结构原则

### 三幕式结构
1. **第一幕：英雄开场** - 全视口英雄区，建立主题认知
2. **第二幕：分章节展开** - 采用重复模式逐步深入
3. **第三幕：中心辐射总结** - 全景总结，中心辐射布局

### 布局系统
- **Bento Grid布局** - 12列网格系统，支持灵活的卡片组合
- **响应式设计** - 1200px-1920px最佳体验，适配移动端
- **卡片尺寸变体** - large(8列)、medium(6列)、small(4列)、wide(12列)

## 视觉设计规范

### 色彩系统
- **主要色彩**：#1d1d1f（深灰）、#86868b（中灰）、#f5f5f7（浅灰）
- **强调色彩**：#007AFF（蓝）、#30D158（绿）
- **渐变运用**：linear-gradient用于文字，radial-gradient用于装饰

### 字体规范
- **字体族**：-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto
- **英雄标题**：96px/700/1.05行高
- **章节标题**：72px/700
- **数据展示**：84px/700
- **正文内容**：18px-24px

### 间距系统
- **基础间距**：24px的倍数（24px、48px、120px）
- **卡片内边距**：48px
- **网格间距**：24px
- **圆角半径**：24px

## 交互动效规范

### 动画系统
- **滚动触发动画**：fade-in with translateY(40px)
- **缓动函数**：cubic-bezier(0.25, 0.46, 0.45, 0.94)
- **动画时长**：0.8s主要动画，0.3s次要动画
- **视差滚动**：装饰球体的轻微视差效果

### 悬停状态
- **卡片悬停**：translateY(-8px) + 阴影增强
- **过渡时长**：0.3s ease

## 代码组织规范

### CSS结构
1. 全局重置和基础样式
2. 动画系统定义
3. 第一幕：英雄开场样式
4. 第二幕：章节和卡片样式
5. 第三幕：总结区域样式
6. 响应式媒体查询

### HTML结构
```html
<div class="page-container">
  <section class="hero-section">...</section>
  <section class="chapter">...</section>
  <section class="finale-section">...</section>
</div>
```

### JavaScript增强
- 滚动触发动画（IntersectionObserver）
- 简单视差滚动效果
- 渐进加载优化

## 内容编写规范

### 标题层级
- H1：页面主标题（英雄标题）
- H2：章节标题
- H3：特性标题
- H4：子特性标题

### 数据展示
- 突出关键数字，配以简洁的单位说明
- 使用渐变色彩增强视觉冲击力
- 合理安排数据卡片的布局位置

### 文案原则
- 简洁有力，避免冗余描述
- 突出核心价值和关键特性
- 使用引言卡片增强说服力

## 性能优化规范

### 图片处理
- 使用CSS渐变替代图片装饰
- 适当使用emoji图标作为视觉元素
- 确保图片懒加载和响应式适配

### 动画优化
- 合理控制动画元素数量
- 使用transform替代position变化
- 避免重复触发布局重排

## 质量检查清单

### 设计一致性
- [ ] 色彩系统符合苹果设计规范
- [ ] 字体大小和权重层级清晰
- [ ] 间距系统统一规整
- [ ] 卡片圆角和阴影一致

### 交互体验
- [ ] 滚动动画流畅自然
- [ ] 悬停状态反馈明确
- [ ] 响应式断点合理
- [ ] 加载性能优良

### 内容质量
- [ ] 信息层级清晰合理
- [ ] 数据展示突出重点
- [ ] 文案简洁有力
- [ ] 视觉引导有效
