      
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>[在此处填写报告标题] - [日期]</title>
    <link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/tailwindcss/2.2.19/tailwind.min.css">
    <link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-100-M/font-awesome/6.0.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js" xintegrity="sha512-BNaRQnYJYiPSqHHDb58B0yaPfCu+Wgds8Gp/gU33kqBtgNS4tSPHuGibyoVBL5rLesXWW/sGuLhYFChxgYnz2Q==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.min.js"></script>
    <style>
        :root {
            /* 极简主义风格配色 (from report) */
            --bg-primary: #f5f4ee; /* 略暖的米白色 */
            --bg-secondary: #faf9f6; /* 更浅的卡片背景色 */
            --bg-tertiary: #f1f3f5; /* 第三背景色，用于标签等 */
            --text-primary: #212529; /* 主要文字颜色 */
            --text-secondary: #495057; /* 次要文字颜色 */
            --accent-primary: #ff8906; /* 主要强调色 - 橙色 */
            --accent-secondary: #f76707; /* 次要强调色 - 深橙色 */
            --accent-tertiary: #e8590c; /* 第三强调色 - 焦橙色 */
            --accent-blue: #339af0;     /* 特定元素的蓝色强调 */
            --accent-purple: #7048e8; /* 词云的紫色强调 */
            --accent-cyan: #22b8cf;     /* 词云的青色强调 */
            --highlight-keyword-bg: #ffe8cc; /* 关键词高亮背景 */
            --highlight-name-color: #7048e8; /* 人名高亮颜色 */
            --card-padding: 24px; /* 卡片内边距 */
            --grid-gap: 16px; /* 网格间距 */
            --card-radius: 12px; /* 卡片圆角 */
            --card-shadow: 0 4px 6px rgba(0, 0, 0, 0.05); /* 卡片阴影 */
            --font-family: 'Inter', 'SF Pro Display', 'Segoe UI', sans-serif; /* 定义字体变量 */
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-family); /* 使用字体变量 */
            background-color: var(--bg-primary);
            color: var(--text-primary);
            line-height: 1.6;
            font-size: 16px;
            width: 100%;
            max-width: 1000px; /* 内容最大宽度 */
            margin: 0 auto; /* 内容居中 */
            padding: 20px; /* 内容周围内边距 */
        }
        body.modal-active {
            overflow: hidden; /* Prevent body scroll when modal is open */
        }

        h1, h2, h3, h4 {
            font-weight: 600;
            letter-spacing: 0.5px;
        }

        h1 {
            font-size: 2.5rem; /* 主卡片大标题 */
            margin-bottom: 0.5rem;
            color: var(--accent-primary);
        }

        h2 { /* H2通用样式, 也用于主题卡片 */
            font-size: 1.75rem; /* 版块标题 */
            margin-bottom: 1rem;
            color: var(--accent-primary); /* 区块和主题卡片标题颜色 */
        }

        h3 { /* 用于子标题或特定元素，如统计卡片 */
            font-size: 1.25rem; /* 子版块标题 */
            margin-bottom: 0.75rem;
            color: var(--accent-blue); /* 子标题使用蓝色以形成对比 */
        }

        .grid-container {
            display: grid;
            grid-template-columns: repeat(12, 1fr); /* 12列网格系统 */
            grid-auto-rows: minmax(100px, auto); /* 行高根据内容自动调整, 至少100px */
            gap: var(--grid-gap);
            margin-top: 20px;
            grid-template-areas: /* 更新的网格区域 */
                "main      main      main      main      main      main      main      main      main      main      main      main"
                "topics    topics    topics    topics    topics    topics    topics    topics    topics    topics    topics    topics"
                "mindmap   mindmap   mindmap   mindmap   mindmap   mindmap   mindmap   mindmap   mindmap   mindmap   mindmap   mindmap"
                "quote     quote     quote     quote     quote     quote     links     links     links     links     links     links"
                "stats     stats     stats     stats     stats     stats     stats     wordcloud wordcloud wordcloud wordcloud wordcloud";
        }

        .card {
            background-color: var(--bg-secondary);
            border-radius: var(--card-radius);
            padding: var(--card-padding);
            box-shadow: var(--card-shadow);
            position: relative; /* 用于图标定位和伪元素 */
            overflow: hidden; /* 确保伪元素不超出卡片圆角 */
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            display: flex; /* 为卡片内flex布局添加 */
            flex-direction: column; /* 垂直堆叠项目 */
        }

        .card:hover {
            transform: translateY(-5px); /* 鼠标悬停时轻微上浮效果 */
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1); /* 鼠标悬停时增强阴影 */
        }

        /* 卡片顶部强调边框 */
        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background: var(--accent-primary);
        }

        /* 卡片右下角装饰性图标 */
        .card-icon {
            position: absolute;
            bottom: var(--card-padding);
            right: var(--card-padding);
            font-size: 4rem;
            opacity: 0.07;
            color: var(--accent-primary);
            z-index: 0; /* Ensure it's behind content if necessary */
        }

        /* 网格区域分配 */
        .main-card { grid-area: main; }
        .topic-cards-wrapper {
            grid-area: topics;
            display: grid;
            grid-template-columns: repeat(12, 1fr);
            gap: var(--grid-gap);
        }
        .mindmap-card-container { grid-area: mindmap; } /* Container for the mindmap card */
        .quote-card { grid-area: quote; }
        .links-card { grid-area: links; }
        .stats-card { grid-area: stats; }
        .wordcloud-card { grid-area: wordcloud; }

        .topic-card {
            grid-column: span 6; /* 默认占据6列 */
            min-height: 250px;
        }
        .topic-card > .topic-card-content-wrapper {
            flex-grow: 1; 
            padding-bottom: 1rem; 
        }
        .topic-cards-wrapper > .topic-card:nth-last-child(1):nth-child(odd) {
            grid-column: span 12;
        }

        .main-card h1 { text-align: center; }
        .main-card .date { text-align: center; }
        .date { font-size: 1.1rem; color: var(--text-secondary); margin-bottom: 1rem; }
        .meta-info { display: flex; flex-wrap: wrap; gap: 15px; margin-bottom: 1rem; justify-content: center; }
        .meta-info span { background-color: var(--bg-tertiary); padding: 5px 10px; border-radius: 20px; font-size: 0.9rem; color: var(--accent-blue); }
        .summary { margin-top: 1rem; line-height: 1.7; text-align: left; }
        .topic-category { display: inline-block; background-color: var(--accent-tertiary); color: var(--bg-primary); padding: 3px 8px; border-radius: 4px; font-size: 0.8rem; margin-bottom: 0.5rem; }
        .topic-keywords { display: flex; flex-wrap: wrap; gap: 8px; margin-top: 0.75rem; margin-bottom: 0.75rem; }
        .keyword { background-color: var(--bg-tertiary); color: var(--accent-primary); padding: 3px 8px; border-radius: 4px; font-size: 0.8rem; }
        .highlight-keyword { background-color: var(--highlight-keyword-bg); padding: 1px 2px; border-radius: 2px; font-weight: 500; display: inline; line-height: 1.2; box-decoration-break: clone; -webkit-box-decoration-break: clone; }
        .highlight-name { color: var(--highlight-name-color); font-weight: 500; }
        .topic-mentions { font-size: 0.9rem; color: var(--text-secondary); }
        .quote { position: relative; padding-left: 20px; margin: 10px 0; font-style: italic; color: var(--text-secondary); }
        .quote::before { content: '"'; position: absolute; left: 0; top: 0; font-size: 1.5rem; color: var(--accent-tertiary); }
        .quote-author { text-align: right; font-size: 0.9rem; color: var(--accent-tertiary); margin-top: 5px; }
        .link-item { display: flex; align-items: center; margin-bottom: 10px; padding: 8px; border-radius: 6px; background-color: var(--bg-tertiary); transition: background-color 0.2s ease; text-decoration: none; }
        .link-item:hover { background-color: rgba(51, 154, 240, 0.1); }
        .link-item a { text-decoration: none; color: inherit; display: flex; align-items: center; width: 100%; }
        .link-item a:hover .link-title { text-decoration: underline; color: var(--accent-blue); }
        .link-icon { margin-right: 10px; color: var(--accent-blue); }
        .link-title { flex-grow: 1; color: var(--text-primary); }
        .link-title.is-link { color: var(--accent-blue); }
        .link-title.is-link:hover { text-decoration: underline; }
        .user-stats-table { width: 100%; border-collapse: collapse; margin-top: 0.5rem; }
        .user-stats-table th { text-align: center; padding: 0.75rem 0.5rem; border-bottom: 2px solid var(--accent-primary); color: var(--text-primary); font-weight: 600; font-size: 0.9rem; }
        .user-stats-table td { padding: 0.75rem 0.5rem; border-bottom: 1px solid var(--bg-tertiary); color: var(--text-secondary); font-size: 0.85rem; vertical-align: top; text-align: left; }
        .user-stats-table .user-name-col { width: 25%; font-weight: 500; word-break: break-all; }
        .user-stats-table .message-count-col { text-align: center; width: 15%; white-space: nowrap; }
        .user-stats-table .contribution-col { width: 60%; word-break: break-all; }
        .user-stats-table tr:last-child td { border-bottom: none; }
        .wordcloud { display: flex; flex-wrap: wrap; justify-content: center; gap: 10px; padding: 20px 0; }
        .wordcloud-item { padding: 5px 10px; border-radius: 4px; font-weight: 500; transition: transform 0.2s ease; }
        .wordcloud-item:hover { transform: scale(1.1); }
        .size-1 { font-size: 0.9rem; color: var(--text-secondary); }
        .size-2 { font-size: 1.1rem; color: var(--accent-cyan); }
        .size-3 { font-size: 1.3rem; color: var(--accent-blue); }
        .size-4 { font-size: 1.5rem; color: var(--accent-purple); }
        .size-5 { font-size: 1.8rem; color: var(--accent-tertiary); }
        .action-buttons-container { text-align: center; margin: 20px 0; padding-bottom: 20px; }
        .action-button { background-color: var(--accent-primary); color: white; border: none; padding: 10px 20px; border-radius: var(--card-radius); font-size: 1rem; font-weight: 500; cursor: pointer; transition: background-color 0.3s ease, transform 0.2s ease; margin: 5px 10px; box-shadow: var(--card-shadow); display: inline-flex; align-items: center; justify-content: center; }
        .action-button:hover { background-color: var(--accent-secondary); transform: translateY(-2px); }
        .action-button i, .action-button .fas { margin-right: 8px; }
        .footer { margin-top: 30px; text-align: center; color: var(--text-secondary); font-size: 0.9rem; }
        #report-content-wrapper .footer { margin-top: 30px; padding-bottom: 20px; }

        /* Mermaid Diagram Specific Styles */
        .mindmap-card { /* This class is applied to the card holding the mindmap */
            min-height: 400px; /* Adjust as needed */
            /* max-width: 900px; /* Let grid control width, or set if needed */
        }
        .mindmap-card h2 i { margin-right: 8px; }
        .mindmap-controls { display: flex; gap: 10px; margin-bottom: 15px; align-items: center; flex-wrap: wrap; }
        .mindmap-controls button { background-color: var(--accent-blue); color: white; border: none; padding: 8px 12px; border-radius: var(--card-radius); font-size: 0.9rem; cursor: pointer; transition: background-color 0.2s ease; font-weight: 500; display: inline-flex; align-items: center; justify-content: center; }
        .mindmap-controls button:hover { background-color: #228be6; /* Darker blue for hover */ }
        .mindmap-controls button i { margin-right: 5px; }
        .mindmap-controls .fullscreen-toggle-btn { margin-left: auto; }
        .mermaid-container { flex-grow: 1; display: flex; justify-content: center; align-items: center; overflow: auto; background-color: #fff; border-radius: 8px; padding: 10px; border: 1px solid var(--bg-tertiary); min-height: 300px; cursor: grab; }
        .mermaid-container.dragging { cursor: grabbing; }
        .mermaid-container:empty::before { content: "在此处生成或粘贴Mermaid导图代码。"; color: var(--text-secondary); font-style: italic; text-align: center; padding: 20px; }
        .mermaid-container svg { max-width: none; height: auto; display: block; transition: transform 0.2s ease-out; }

        /* Feedback messages for Mermaid */
        #mermaid-main-feedback-message, #mermaid-modal-feedback-message { text-align: center; margin-top: 15px; padding: 8px; border-radius: 4px; font-weight: 500; font-size: 0.9rem; display: none; }
        #mermaid-main-feedback-message.success, #mermaid-modal-feedback-message.success { color: #155724; background-color: #d4edda; border: 1px solid #c3e6cb; display: block; }
        #mermaid-main-feedback-message.error, #mermaid-modal-feedback-message.error { color: #721c24; background-color: #f8d7da; border: 1px solid #f5c6cb; display: block; }
        #mermaid-main-feedback-message.info, #mermaid-modal-feedback-message.info { color: #0c5460; background-color: #d1ecf1; border: 1px solid #bee5eb; display: block; }
        
        /* Report's own feedback message styling */
        #feedback-message { text-align: center; margin-top: 10px; font-weight: 500; }
        #feedback-message.success { color: green; }
        #feedback-message.error { color: red; }

        /* Fullscreen Modal Styles for Mermaid */
        .modal-overlay { position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.75); display: none; justify-content: center; align-items: center; z-index: 1000; padding: 20px; }
        .modal-overlay.active { display: flex; }
        .modal-content { /* This class is also used by the Mermaid modal's inner card */
            width: 95%; height: 90%; max-width: 1200px; 
            /* display: flex; flex-direction: column; /* Already part of .card */
        }
        .modal-close-btn { position: absolute; top: 15px; right: 20px; background: none; border: none; font-size: 2.2rem; color: var(--text-secondary); cursor: pointer; line-height: 1; padding: 5px; z-index: 1010; }
        .modal-close-btn:hover { color: var(--text-primary); }
        #modalMermaidContainer { flex-grow: 1; min-height: 0; /* Ensure it uses space within modal-content */ }
        #modalMermaidContainer:empty::before { content: "导图内容将在此显示。"; color: var(--text-secondary); font-style: italic; text-align: center; padding: 20px; display: flex; justify-content: center; align-items: center; height: 100%; }

        @keyframes fadeIn { from { opacity: 0; transform: translateY(20px); } to { opacity: 1; transform: translateY(0); } }
        .card { animation: fadeIn 0.5s ease forwards; }
        .main-card { animation-delay: 0.1s; }
        .topic-cards-wrapper .topic-card:nth-child(1) { animation-delay: 0.2s; }
        .topic-cards-wrapper .topic-card:nth-child(2) { animation-delay: 0.3s; }
        .topic-cards-wrapper .topic-card:nth-child(3) { animation-delay: 0.4s; }
        .topic-cards-wrapper .topic-card:nth-child(4) { animation-delay: 0.5s; }
        .mindmap-card-container .card { animation-delay: 0.6s; } /* Mindmap card animation */
        .quote-card { animation-delay: 0.7s; }
        .links-card { animation-delay: 0.8s; }
        .stats-card { animation-delay: 0.9s; }
        .wordcloud-card { animation-delay: 1.0s; }
        #report-content-wrapper .footer { animation: fadeIn 0.5s ease forwards; animation-delay: 1.1s; } /* Footer animation */
        .action-buttons-container { animation: fadeIn 0.5s ease forwards; animation-delay: 1.2s; } /* Buttons animation */


        @media (max-width: 768px) {
            body { padding: 10px; font-size: 14px; }
            h1 { font-size: 1.8rem; }
            h2 { font-size: 1.4rem; }
            h3 { font-size: 1.1rem; }
            .grid-container {
                grid-template-columns: 1fr;
                grid-template-areas:
                    "main"
                    "topics"
                    "mindmap"
                    "quote"
                    "links"
                    "stats"
                    "wordcloud";
            }
            .topic-cards-wrapper { grid-template-columns: 1fr; }
            .topic-card { grid-column: 1 / -1; min-height: auto; }
            .topic-card > .topic-card-content-wrapper { padding-bottom: 1rem; }
            .topic-mentions { position: static; margin-top: 10px; margin-bottom: 10px; text-align: left; }
            .card-icon { font-size: 2.5rem; opacity:0.05; /* position: relative; margin-top: auto; margin-left: auto; padding-top: 10px; bottom:15px; right:15px; (Original card-icon positioning is fine for most cards)*/ }
            .mindmap-card .card-icon { bottom: 15px; right: 15px; /* Ensure mindmap card icon is positioned like others */ }

            .user-stats-table th, .user-stats-table td { padding: 0.5rem 0.25rem; font-size: 0.8rem; }
            .user-stats-table td { text-align: left; }
            .user-stats-table .message-count-col { text-align: center; }
            .action-button { padding: 8px 15px; font-size: 0.9rem; }
            .action-buttons-container { display: flex; flex-wrap: wrap; justify-content: center; gap: 10px; }
            .action-buttons-container .action-button { width: calc(50% - 10px); min-width: 180px; margin: 5px 0; }
            
            /* Mermaid specific responsive */
            .mindmap-card .card { padding: 15px; }
            .mindmap-controls button { font-size: 0.8rem; padding: 6px 10px; }
            .modal-content { width: 100%; height: 95%; }
            .modal-close-btn { font-size: 1.8rem; top:10px; right:10px;}
        }
        @media (max-width: 480px) {
            .action-buttons-container .action-button { width: 80%; }
            .user-stats-table { display: block; overflow-x: auto; }
            .user-stats-table th, .user-stats-table td { white-space: normal; }
            .user-stats-table thead, .user-stats-table tbody, .user-stats-table tr { display: block; }
            .user-stats-table tr { border-bottom: 1px solid var(--bg-tertiary); }
            .user-stats-table th { display: none; }
            .user-stats-table td { display: block; text-align: left; border-bottom: none; padding-left: 0.5rem; }
            .user-stats-table td.message-count-col { text-align: left; }
            .user-stats-table td::before { content: attr(data-label); font-weight: bold; display: inline-block; width: 100px; margin-right: 10px; color: var(--text-primary); text-align: left; }
            .user-stats-table tr:last-child { border-bottom: none; }
        }
    </style>
</head>
<body>
    <div id="report-content-wrapper">
        <div class="grid-container">
            <div class="card main-card">
                <h1 contenteditable="true">[主报告标题 - 例如：AI产品团日报]</h1>
                <div class="date">[日期 - 例如：2025年5月19日]</div>
                <div class="meta-info">
                    <span><i class="fas fa-comment"></i> 消息数: [数字]+</span>
                    <span><i class="fas fa-users"></i> 活跃用户: [数字]+</span>
                    <span><i class="fas fa-fire"></i> 热点话题: [数字]</span>
                </div>
                <p class="summary">[在此处填写当日讨论的总体摘要。保持简洁明了，突出群组的主要主题和氛围。]</p>
                <i class="fas fa-microchip card-icon"></i>
            </div>

            <div class="topic-cards-wrapper">
                <div class="card topic-card">
                    <div class="topic-card-content-wrapper"> 
                        <h2 contenteditable="true"><i class="fas fa-lightbulb"></i> [主题1标题]</h2>
                        <div class="topic-category">[分类1 - 例如：工具技巧]</div>
                        <p>关于<span class="highlight-keyword">插件功能</span>的讨论，特别是<span class="highlight-name">@用户A</span>提到的<span class="highlight-keyword">上下文处理</span>能力。</p>
                        <div class="topic-keywords">
                            <span class="keyword">[关键词1A]</span>
                            <span class="keyword">[关键词1B]</span>
                            <span class="keyword">[关键词1C]</span>
                        </div>
                        <div class="topic-mentions"><i class="fas fa-bullhorn"></i> 提及次数: [数量1]</div>
                    </div>
                    <i class="fas fa-tools card-icon"></i> 
                </div>
                <div class="card topic-card">
                    <div class="topic-card-content-wrapper"> 
                        <h2 contenteditable="true"><i class="fas fa-rocket"></i> [主题2标题]</h2>
                        <div class="topic-category">[分类2 - 例如：新功能]</div>
                        <p><span class="highlight-name">@用户B</span>分享了<span class="highlight-keyword">AI搜索</span>的最新进展和<span class="highlight-keyword">地区限制</span>问题。</p>
                        <div class="topic-keywords">
                            <span class="keyword">[关键词2A]</span>
                            <span class="keyword">[关键词2B]</span>
                        </div>
                        <div class="topic-mentions"><i class="fas fa-bullhorn"></i> 提及次数: [数量2]</div>
                    </div>
                    <i class="fas fa-star card-icon"></i> 
                </div>
                <div class="card topic-card">
                    <div class="topic-card-content-wrapper"> 
                        <h2 contenteditable="true"><i class="fas fa-comments-dollar"></i> [主题3标题]</h2>
                        <div class="topic-category">[分类3 - 例如：工具体验]</div>
                        <p>大家对<span class="highlight-keyword">Agent技术</span>的<span class="highlight-keyword">商业化前景</span>进行了探讨，<span class="highlight-name">@用户C</span>提出了独到见解。</p>
                        <div class="topic-keywords">
                            <span class="keyword">[关键词3A]</span>
                            <span class="keyword">[关键词3B]</span>
                            <span class="keyword">[关键词3C]</span>
                        </div>
                        <div class="topic-mentions"><i class="fas fa-bullhorn"></i> 提及次数: [数量3]</div>
                    </div>
                    <i class="fas fa-comments card-icon"></i> 
                </div>
            </div>
            
            <div class="mindmap-card-container card"> <div class="mindmap-card"> 
                    <h2><i class="fas fa-sitemap"></i> 核心概念关系图</h2>
                    <div class="mindmap-controls">
                        <button id="zoomInBtn"><i class="fas fa-search-plus"></i> 放大</button>
                        <button id="zoomOutBtn"><i class="fas fa-search-minus"></i> 缩小</button>
                        <button id="downloadDiagramBtn"><i class="fas fa-download"></i> 下载导图</button>
                        <button id="fullscreenOpenBtn" class="fullscreen-toggle-btn"><i class="fas fa-expand"></i> 全屏</button> 
                    </div>
                    <div class="mermaid-container" id="mainMermaidContainer">
                        <div class="mermaid" id="mindmapDiagram">
                            mindmap
                                root((中心主题))
                                    (点击下方按钮或在全屏模式下编辑内容)
                        </div>
                    </div>
                    <div id="mermaid-main-feedback-message"></div>
                    <i class="fas fa-project-diagram card-icon"></i>
                </div>
            </div>
            <div class="card quote-card">
                <h2 contenteditable="false"><i class="fas fa-quote-left"></i> 精彩引用</h2>
                <div class="quote">
                    "[引言1内容。使其具有影响力或代表性。]"
                    <div class="quote-author">- @[发言人1]</div>
                </div>
                <div class="quote">
                    "[引言2内容。]"
                    <div class="quote-author">- @[发言人2]</div>
                </div>
                <div class="quote">
                    "[引言3内容。]"
                    <div class="quote-author">- @[发言人3]</div>
                </div>
                <i class="fas fa-comment-dots card-icon"></i> 
            </div>

            <div class="card links-card">
                <h2 contenteditable="false"><i class="fas fa-link"></i> 重要链接与资源</h2>
                <div class="link-item">
                    <a href="[URL链接1]" target="_blank">
                        <i class="fas fa-external-link-alt link-icon"></i> 
                        <span class="link-title">[链接1标题]</span>
                    </a>
                </div>
                <div class="link-item">
                    <a href="[URL链接2]" target="_blank">
                        <i class="fas fa-external-link-alt link-icon"></i>
                        <span class="link-title">[链接2标题]</span>
                    </a>
                </div>
                <div class="link-item"> <i class="fas fa-file link-icon"></i> 
                    <span class="link-title">[文档1标题 - 例如：共享提示词V5]</span> 
                </div>
                <i class="fas fa-share-alt card-icon"></i> 
            </div>

            <div class="card stats-card">
                <h2 contenteditable="false"><i class="fas fa-chart-line"></i> 活跃之星</h2>
                <table class="user-stats-table">
                    <thead>
                        <tr>
                            <th class="user-name-col">用户</th>
                            <th class="message-count-col">发言数</th>
                            <th class="contribution-col">主要贡献</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td data-label="用户" class="user-name-col">@发言人1</td>
                            <td data-label="发言数" class="message-count-col">[数量1]+</td>
                            <td data-label="主要贡献" class="contribution-col">[用户1的主要贡献内容 - 例如：分享 PageTalk 插件和万能 Prompt 生成器]</td>
                        </tr>
                        <tr>
                            <td data-label="用户" class="user-name-col">@发言人2</td>
                            <td data-label="发言数" class="message-count-col">[数量2]+</td>
                            <td data-label="主要贡献" class="contribution-col">[用户2的主要贡献内容 - 例如：PageTalk 插件开发者，分享使用体验]</td>
                        </tr>
                        <tr>
                            <td data-label="用户" class="user-name-col">@发言人3</td>
                            <td data-label="发言数" class="message-count-col">[数量3]+</td>
                            <td data-label="主要贡献" class="contribution-col">[用户3的主要贡献内容 - 例如：参与 Agent 讨论和工具测试]</td>
                        </tr>
                    </tbody>
                </table>
                <i class="fas fa-user-friends card-icon"></i> 
            </div>

            <div class="card wordcloud-card">
                <h2 contenteditable="false"><i class="fas fa-cloud"></i> 词云</h2>
                <div class="wordcloud">
                    <span class="wordcloud-item size-5">[关键词A]</span>
                    <span class="wordcloud-item size-5">[关键词B]</span>
                    <span class="wordcloud-item size-4">[关键词C]</span>
                    <span class="wordcloud-item size-4">[关键词D]</span>
                    <span class="wordcloud-item size-3">[关键词E]</span>
                    <span class="wordcloud-item size-3">[关键词F]</span>
                    <span class="wordcloud-item size-2">[关键词G]</span>
                    <span class="wordcloud-item size-2">[关键词H]</span>
                    <span class="wordcloud-item size-1">[关键词I]</span>
                </div>
                <i class="fas fa-tags card-icon"></i> 
            </div>
        </div> 
        
        <div class="footer">
            <p>数据来源：[您的数据来源 - 例如：AI产品团聊天记录] | 生成日期：[生成日期] | 统计周期：[统计周期 - 例如：[日期]全天]</p>
            <p>[免责声明 - 例如：本报告由AI自动生成，仅供参考，不代表所有群成员观点。]</p>
        </div>
    </div> 

    <div class="action-buttons-container">
        <button id="viewPreviousReportBtn" class="action-button"><i class="fas fa-calendar-alt"></i> 查看昨日日报</button>
        <button id="screenshotToClipboardBtn" class="action-button"><i class="fas fa-clipboard"></i> 拷贝截图</button>
        <button id="screenshotDownloadBtn" class="action-button"><i class="fas fa-camera"></i> 下载截图</button>
        <button id="copyHtmlBtn" class="action-button"><i class="fas fa-copy"></i> 拷贝源码</button>
        <button id="downloadHtmlBtn" class="action-button"><i class="fas fa-download"></i> 下载报告</button>
    </div>
    <div id="feedback-message"></div> <div id="fullscreenModal" class="modal-overlay">
        <div class="modal-content card"> <button id="fullscreenCloseBtn" class="modal-close-btn" aria-label="关闭全屏">&times;</button>
            <h2><i class="fas fa-sitemap"></i> 核心概念关系图 (全屏)</h2>
            <div class="mindmap-controls">
                 <button id="modalZoomInBtn"><i class="fas fa-search-plus"></i> 放大</button>
                <button id="modalZoomOutBtn"><i class="fas fa-search-minus"></i> 缩小</button>
                <button id="modalDownloadDiagramBtn"><i class="fas fa-download"></i> 下载导图</button>
            </div>
            <div class="mermaid-container" id="modalMermaidContainer">
                </div>
            <div id="mermaid-modal-feedback-message"></div>
        </div>
    </div>
    <script>
    // --- Mermaid Diagram JavaScript START ---
    // --- Global Variables for Mermaid ---
    let mainCurrentScale = 1;
    let modalCurrentScale = 1;
    let currentMindmapDefinition = ''; // Store the current definition

    // --- DOM Elements for Mermaid ---
    const mainZoomInBtn = document.getElementById('zoomInBtn');
    const mainZoomOutBtn = document.getElementById('zoomOutBtn');
    const mainDownloadDiagramBtn = document.getElementById('downloadDiagramBtn'); // Renamed from downloadBtn
    const mainMermaidContainer = document.getElementById('mainMermaidContainer');
    const mermaidMainFeedbackMessage = document.getElementById('mermaid-main-feedback-message'); // Renamed
    const mainMindmapDiagramDiv = document.getElementById('mindmapDiagram'); 

    const fullscreenOpenBtn = document.getElementById('fullscreenOpenBtn');
    const fullscreenModal = document.getElementById('fullscreenModal');
    const fullscreenCloseBtn = document.getElementById('fullscreenCloseBtn');
    const modalMermaidContainer = document.getElementById('modalMermaidContainer');
    const modalZoomInBtn = document.getElementById('modalZoomInBtn');
    const modalZoomOutBtn = document.getElementById('modalZoomOutBtn');
    const modalDownloadDiagramBtn = document.getElementById('modalDownloadDiagramBtn'); // Renamed
    const mermaidModalFeedbackMessage = document.getElementById('mermaid-modal-feedback-message'); // Renamed

    // --- Mermaid Initialization ---
    if (typeof mermaid !== 'undefined') {
        mermaid.initialize({
            startOnLoad: false, // Will render manually after content is set
            theme: 'base', // Options: 'default', 'forest', 'dark', 'neutral', 'base'
            fontFamily: '"Inter", sans-serif',
            mindmap: { padding: 15 }
            // For other diagram types, you might add specific configs here
            // e.g., sequence: { actorMargin: 50 }
        });
    } else {
        console.error("Mermaid library not loaded.");
        if(mermaidMainFeedbackMessage) mermaidMainFeedbackMessage.textContent = "错误：Mermaid库未能加载。";
    }
    

    // --- Helper Functions for Mermaid ---
    function showMermaidFeedback(element, message, type = 'info', duration = 3000) {
        if (element) {
            element.textContent = message;
            element.className = type; // success, error, info
            element.style.display = 'block';
            setTimeout(() => {
                element.textContent = '';
                element.className = '';
                element.style.display = 'none';
            }, duration);
        }
    }

    function applySvgScale(svgElement, scale) {
        if (svgElement) {
            svgElement.style.transform = `scale(${scale})`;
            svgElement.style.transformOrigin = 'center center'; // Ensure scaling is from center
        }
    }

    function makeDraggable(containerElement) {
        let isDragging = false;
        let startX, startY, scrollLeftStart, scrollTopStart;

        if (!containerElement) return;

        containerElement.addEventListener('mousedown', (e) => {
            if (e.button !== 0) return; // Only main mouse button
            // Prevent dragging if clicking on interactive elements within the SVG or buttons
            if (e.target.closest('button') || e.target.closest('a') || e.target.closest('[onclick]')) return;
            
            // Check if clicking on scrollbars (simple check, might need refinement)
             if (e.target === containerElement) { // Only drag if mousedown is on container itself, not SVG content
                const verticalScrollbarVisible = containerElement.scrollHeight > containerElement.clientHeight;
                const horizontalScrollbarVisible = containerElement.scrollWidth > containerElement.clientWidth;
                const clickedOnVerticalScrollbar = verticalScrollbarVisible && e.offsetX >= containerElement.clientWidth - 17; // Approx scrollbar width
                const clickedOnHorizontalScrollbar = horizontalScrollbarVisible && e.offsetY >= containerElement.clientHeight - 17;
                if (clickedOnVerticalScrollbar || clickedOnHorizontalScrollbar) return;
            }


            isDragging = true;
            startX = e.pageX; startY = e.pageY;
            scrollLeftStart = containerElement.scrollLeft; scrollTopStart = containerElement.scrollTop;
            containerElement.classList.add('dragging');
            // e.preventDefault(); // Be careful with preventDefault on container, might affect text selection inside SVG if not handled well
        });

        document.addEventListener('mousemove', (e) => {
            if (!isDragging) return;
            e.preventDefault(); // Prevent text selection during drag
            const deltaX = e.pageX - startX; const deltaY = e.pageY - startY;
            containerElement.scrollLeft = scrollLeftStart - deltaX;
            containerElement.scrollTop = scrollTopStart - deltaY;
        });

        document.addEventListener('mouseup', (e) => {
            if (e.button !== 0 || !isDragging) return;
            isDragging = false;
            containerElement.classList.remove('dragging');
        });
         // Touch events for dragging on mobile
        containerElement.addEventListener('touchstart', (e) => {
            if (e.target.closest('button') || e.target.closest('a') || e.target.closest('[onclick]')) return;
            if (e.touches.length === 1) {
                isDragging = true;
                const touch = e.touches[0];
                startX = touch.pageX;
                startY = touch.pageY;
                scrollLeftStart = containerElement.scrollLeft;
                scrollTopStart = containerElement.scrollTop;
                containerElement.classList.add('dragging');
            }
        }, { passive: true }); // Use passive true if not calling preventDefault

        document.addEventListener('touchmove', (e) => {
            if (!isDragging || e.touches.length !== 1) return;
            // e.preventDefault(); // To prevent page scroll if dragging content
            const touch = e.touches[0];
            const deltaX = touch.pageX - startX;
            const deltaY = touch.pageY - startY;
            containerElement.scrollLeft = scrollLeftStart - deltaX;
            containerElement.scrollTop = scrollTopStart - deltaY;
        }/*, { passive: false }*/); // Set passive: false if e.preventDefault() is used

        document.addEventListener('touchend', (e) => {
            if (!isDragging) return;
            isDragging = false;
            containerElement.classList.remove('dragging');
        });
    }
    
    async function downloadMermaidAsSVG(svgElement, baseFilename, feedbackElem) {
        if (svgElement && svgElement.innerHTML.trim() !== "") {
            try {
                // Apply current scale to the SVG for download if desired, or download at 100%
                // For simplicity, we download the SVG as it is currently rendered (potentially scaled in view, but SVG itself is 100%)
                const svgData = new XMLSerializer().serializeToString(svgElement);
                const blob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' });
                const url = URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.href = url;
                link.download = `${baseFilename}.svg`;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                URL.revokeObjectURL(url);
                showMermaidFeedback(feedbackElem, `${baseFilename}.svg 已下载!`, 'success');
            } catch (error) {
                console.error(`Error during SVG download for ${baseFilename}:`, error);
                showMermaidFeedback(feedbackElem, `下载 ${baseFilename}.svg 失败: ${error.message}`, 'error');
            }
        } else {
            showMermaidFeedback(feedbackElem, '无法下载：图表内容为空。', 'error');
        }
    }

    async function renderDiagram(definition, targetContainer, scaleVariableRef, feedbackElem, uniqueSvgId) {
        if (!targetContainer) {
            console.error("Target container for Mermaid diagram not found.");
            return null;
        }
        targetContainer.innerHTML = '<p style="text-align:center; padding:20px;">图表加载中...</p>';
        targetContainer.classList.remove('empty');

        if (!definition || definition.trim() === "" || definition.trim().toLowerCase() === "mindmap") {
            targetContainer.innerHTML = ''; 
            targetContainer.classList.add('empty'); 
            return null;
        }
        
        currentMindmapDefinition = definition; // Store the definition for fullscreen toggle

        try {
            if (typeof mermaid === 'undefined') throw new Error("Mermaid library is not available.");
            const {svg, bindFunctions} = await mermaid.render(uniqueSvgId, definition);
            targetContainer.innerHTML = svg;
            if (bindFunctions) bindFunctions(targetContainer);

            const svgElement = targetContainer.querySelector('svg');
            if (svgElement) {
                // Reset scale to 1 initially for the new render
                if (targetContainer === mainMermaidContainer) mainCurrentScale = 1;
                else if (targetContainer === modalMermaidContainer) modalCurrentScale = 1;
                
                applySvgScale(svgElement, scaleVariableRef.value); // Apply current scale (which should be 1 after reset)
                makeDraggable(targetContainer); // Make the container draggable
                return svgElement;
            } else {
                targetContainer.innerHTML = '';
                targetContainer.classList.add('empty');
                showMermaidFeedback(feedbackElem, '图表生成了，但未找到SVG内容。', 'error');
                return null;
            }
        } catch (error) {
            console.error("Error rendering Mermaid:", error);
            targetContainer.innerHTML = `<p style="color:red; text-align:center; padding:20px;">渲染图表时出错: ${error.message}</p>`;
            showMermaidFeedback(feedbackElem, `渲染图表时出错: ${error.message}`, 'error', 5000);
            return null;
        }
    }

    // --- Main Mindmap Logic ---
    if(mainZoomInBtn) mainZoomInBtn.addEventListener('click', () => {
        mainCurrentScale += 0.1;
        const svg = mainMermaidContainer.querySelector('svg');
        applySvgScale(svg, mainCurrentScale);
    });

    if(mainZoomOutBtn) mainZoomOutBtn.addEventListener('click', () => {
        if (mainCurrentScale > 0.2) { // Prevent scaling too small
            mainCurrentScale -= 0.1;
            const svg = mainMermaidContainer.querySelector('svg');
            applySvgScale(svg, mainCurrentScale);
        }
    });

    if(mainDownloadDiagramBtn) mainDownloadDiagramBtn.addEventListener('click', () => {
        const svg = mainMermaidContainer.querySelector('svg');
        downloadMermaidAsSVG(svg, '核心概念关系图', mermaidMainFeedbackMessage);
    });

    // --- Fullscreen Modal Logic for Mermaid ---
    if(fullscreenOpenBtn) fullscreenOpenBtn.addEventListener('click', async () => {
        if (!currentMindmapDefinition || currentMindmapDefinition.trim() === "" || currentMindmapDefinition.trim().toLowerCase() === "mindmap") {
             showMermaidFeedback(mermaidMainFeedbackMessage, '请先生成一个有效的导图才能全屏查看。', 'info');
             return;
        }
        if(fullscreenModal) fullscreenModal.classList.add('active');
        document.body.classList.add('modal-active');
        modalCurrentScale = mainCurrentScale; // Sync scale from main view or reset
        await renderDiagram(currentMindmapDefinition, modalMermaidContainer, {value: modalCurrentScale}, mermaidModalFeedbackMessage, 'modalGeneratedSvgId');
    });

    if(fullscreenCloseBtn) fullscreenCloseBtn.addEventListener('click', () => {
        if(fullscreenModal) fullscreenModal.classList.remove('active');
        document.body.classList.remove('modal-active');
        if(modalMermaidContainer) {
            modalMermaidContainer.innerHTML = ''; 
            modalMermaidContainer.classList.add('empty');
        }
    });

    if(modalZoomInBtn) modalZoomInBtn.addEventListener('click', () => {
        modalCurrentScale += 0.1;
        const svg = modalMermaidContainer.querySelector('svg');
        applySvgScale(svg, modalCurrentScale);
    });

    if(modalZoomOutBtn) modalZoomOutBtn.addEventListener('click', () => {
        if (modalCurrentScale > 0.2) {
            modalCurrentScale -= 0.1;
            const svg = modalMermaidContainer.querySelector('svg');
            applySvgScale(svg, modalCurrentScale);
        }
    });

    if(modalDownloadDiagramBtn) modalDownloadDiagramBtn.addEventListener('click', () => {
        const svg = modalMermaidContainer.querySelector('svg');
        downloadMermaidAsSVG(svg, '核心概念关系图-全屏', mermaidModalFeedbackMessage);
    });

    // --- Initial Setup for Mermaid ---
    async function initializeMainDiagram() {
        if (mainMindmapDiagramDiv && mainMermaidContainer) {
            const initialDefinition = mainMindmapDiagramDiv.textContent.trim();
            if (initialDefinition) {
                await renderDiagram(initialDefinition, mainMermaidContainer, { value: mainCurrentScale }, mermaidMainFeedbackMessage, 'initialMainDiagramSvg');
            } else {
                mainMermaidContainer.classList.add('empty'); // Ensure it's marked empty if no initial content
            }
        } else {
             if(mermaidMainFeedbackMessage && !mainMermaidContainer) showMermaidFeedback(mermaidMainFeedbackMessage, '错误：导图容器未找到。', 'error');
        }
    }
    // --- Mermaid Diagram JavaScript END ---


    // --- Report Page JavaScript START ---
    // Helper function: Get CSS variable value
    function getCssVariable(variableName, defaultValue = null) {
        const value = getComputedStyle(document.documentElement).getPropertyValue(variableName).trim();
        return value || defaultValue;
    }

    function getWebpageNameForFilename() {
        const pageTitleTag = document.querySelector('title');
        let baseName = 'AI产品日报'; 
        if (pageTitleTag && pageTitleTag.textContent) {
            const fullTitle = pageTitleTag.textContent.trim();
            const titleParts = fullTitle.split(' - '); 
            if (titleParts.length > 0 && titleParts[0].trim() !== "" && titleParts[0].trim() !== "[在此处填写报告标题]") {
                baseName = titleParts[0].trim(); 
            }
        }
        return baseName;
    }

    function getDateStringForFilename() {
        const reportDateElement = document.querySelector('.main-card .date');
        let dateStr = "未指定日期"; 
        if (reportDateElement && reportDateElement.textContent) {
            const dateContent = reportDateElement.textContent.trim();
            if (dateContent && dateContent !== "[日期 - 例如：2025年5月19日]") { 
                dateStr = dateContent;
            }
        }
        return dateStr;
    }
    
    function sanitizeFilename(filename) {
        return filename.replace(/[^a-z0-9\u4e00-\u9fa5_\-\.\(\)\[\]]/gi, '_').replace(/_+/g, '_');
    }

    const previousReportUrl = "在此处填写昨日日报的URL"; 

    function showReportFeedbackMessage(message, type = 'info', duration = 3000) { // Renamed to avoid conflict
        const feedbackElement = document.getElementById('feedback-message'); // This is the report's general feedback
        if (feedbackElement) {
            feedbackElement.textContent = message;
            feedbackElement.className = type; 
            setTimeout(() => {
                feedbackElement.textContent = '';
                feedbackElement.className = '';
            }, duration);
        }
    }
    
    async function captureScreenshot(elementToCapture) {
        await document.fonts.ready; 
        return html2canvas(elementToCapture, { 
            useCORS: true, 
            scale: window.devicePixelRatio || 1, 
            logging: false, 
            backgroundColor: getCssVariable('--bg-primary', '#f5f4ee'), 
            scrollX: -window.scrollX, 
            scrollY: -window.scrollY,
            windowWidth: document.documentElement.scrollWidth,
            windowHeight: document.documentElement.scrollHeight,
            onclone: function(clonedDoc) {
                const highlightElements = clonedDoc.querySelectorAll('.highlight-keyword');
                highlightElements.forEach(el => {
                    el.style.display = 'inline';
                    el.style.padding = '1px 2px';
                    el.style.lineHeight = '1.2';
                    el.style.boxDecorationBreak = 'clone';
                    el.style.webkitBoxDecorationBreak = 'clone';
                });
            }
        });
    }

    const screenshotToClipboardBtn = document.getElementById('screenshotToClipboardBtn');
    if(screenshotToClipboardBtn) screenshotToClipboardBtn.addEventListener('click', async function() {
        const reportContent = document.getElementById('report-content-wrapper');
        if (!reportContent) {
            showReportFeedbackMessage('截图错误: 未找到报告内容。', 'error');
            return;
        }
        const actionButtons = document.querySelector('.action-buttons-container');
        const feedbackMsgElement = document.getElementById('feedback-message');
        const originalButtonText = this.innerHTML;
        this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 处理中...';
        this.disabled = true;
        if(actionButtons) actionButtons.style.visibility = 'hidden'; 
        if(feedbackMsgElement) feedbackMsgElement.style.visibility = 'hidden';

        try {
            const canvas = await captureScreenshot(reportContent);
            canvas.toBlob(async function(blob) {
                if (blob) {
                    try {
                        if (navigator.clipboard && navigator.clipboard.write) {
                            await navigator.clipboard.write([
                                new ClipboardItem({ 'image/png': blob })
                            ]);
                            showReportFeedbackMessage('截图已拷贝到剪贴板!', 'success');
                        } else {
                            showReportFeedbackMessage('截图已生成，但您的浏览器不支持直接拷贝图片。请尝试下载截图。', 'error', 5000);
                        }
                    } catch (copyError) {
                        console.error('拷贝到剪贴板失败:', copyError);
                        showReportFeedbackMessage('拷贝截图到剪贴板失败: ' + copyError.message, 'error', 5000);
                    }
                } else {
                    throw new Error('无法将Canvas转换为Blob。');
                }
            }, 'image/png');
        } catch (error) {
            console.error('截图捕获失败:', error);
            showReportFeedbackMessage('截图捕获失败: ' + error.message, 'error');
        } finally {
            if(actionButtons) actionButtons.style.visibility = 'visible'; 
            if(feedbackMsgElement) feedbackMsgElement.style.visibility = 'visible'; 
            this.innerHTML = originalButtonText;
            this.disabled = false;
        }
    });

    const screenshotDownloadBtn = document.getElementById('screenshotDownloadBtn');
    if(screenshotDownloadBtn) screenshotDownloadBtn.addEventListener('click', async function() {
        const reportContent = document.getElementById('report-content-wrapper');
        if (!reportContent) {
            showReportFeedbackMessage('截图错误: 未找到报告内容。', 'error');
            return;
        }
        const actionButtons = document.querySelector('.action-buttons-container');
        const feedbackMsgElement = document.getElementById('feedback-message');
        const originalButtonText = this.innerHTML;
        this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 生成中...';
        this.disabled = true;
        if(actionButtons) actionButtons.style.visibility = 'hidden';
        if(feedbackMsgElement) feedbackMsgElement.style.visibility = 'hidden';

        try {
            const canvas = await captureScreenshot(reportContent);
            const image = canvas.toDataURL('image/png');
            const link = document.createElement('a');
            
            const webpageName = getWebpageNameForFilename();
            const dateString = getDateStringForFilename();
            let filename = sanitizeFilename(webpageName + '-' + dateString) + '.png';

            link.href = image;
            link.download = filename;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            showReportFeedbackMessage('截图已下载!', 'success');
        } catch (error) {
            console.error('下载截图失败:', error);
            showReportFeedbackMessage('下载截图失败: ' + error.message, 'error');
        } finally {
            if(actionButtons) actionButtons.style.visibility = 'visible';
            if(feedbackMsgElement) feedbackMsgElement.style.visibility = 'visible';
            this.innerHTML = originalButtonText;
            this.disabled = false;
        }
    });
    
    const downloadHtmlBtn = document.getElementById('downloadHtmlBtn');
    if(downloadHtmlBtn) downloadHtmlBtn.addEventListener('click', function() {
        try {
            const htmlContent = document.documentElement.outerHTML;
            const blob = new Blob([htmlContent], { type: 'text/html;charset=utf-8' });
            const link = document.createElement('a');
            
            const webpageName = getWebpageNameForFilename();
            const dateString = getDateStringForFilename();
            let filename = sanitizeFilename(webpageName + '-' + dateString) + '.html';
            
            link.href = URL.createObjectURL(blob);
            link.download = filename;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(link.href); 
            showReportFeedbackMessage('HTML报告已下载!', 'success');
        } catch (error) {
            console.error('下载HTML失败:', error);
            showReportFeedbackMessage('下载HTML失败: ' + error.message, 'error');
        }
    });

    const copyHtmlBtn = document.getElementById('copyHtmlBtn');
    if(copyHtmlBtn) copyHtmlBtn.addEventListener('click', function() {
        const htmlContent = document.documentElement.outerHTML;
        const Gthis = this; 
        const originalButtonHTML = Gthis.innerHTML; 
        const textarea = document.createElement('textarea');
        textarea.value = htmlContent;
        textarea.style.position = 'fixed'; 
        textarea.style.top = '-9999px'; 
        textarea.style.left = '-9999px';
        document.body.appendChild(textarea);
        try {
            textarea.select();
            textarea.setSelectionRange(0, textarea.value.length); 
            if (document.execCommand('copy')) {
                Gthis.innerHTML = '<i class="fas fa-check"></i> 已拷贝!';
                showReportFeedbackMessage('HTML已拷贝到剪贴板!', 'success', 2000);
            } else {
                Gthis.innerHTML = '<i class="fas fa-times"></i> 拷贝失败';
                showReportFeedbackMessage('拷贝失败。您的浏览器可能不支持此操作。', 'error', 2000);
            }
        } catch (err) {
            Gthis.innerHTML = '<i class="fas fa-times"></i> 拷贝出错';
            showReportFeedbackMessage('拷贝出错: ' + err.message, 'error', 2000);
            console.error('拷贝HTML源码出错:', err);
        } finally {
            setTimeout(function() { 
                Gthis.innerHTML = originalButtonHTML; 
            }, 2000);
            document.body.removeChild(textarea);
        }
    });

    const viewPreviousReportBtn = document.getElementById('viewPreviousReportBtn');
    if(viewPreviousReportBtn) viewPreviousReportBtn.addEventListener('click', function() {
        const urlToOpen = previousReportUrl.trim();
        const placeholderUrlText = "在此处填写昨日日报的URL"; 
        if (urlToOpen && urlToOpen !== placeholderUrlText && (urlToOpen.startsWith('http://') || urlToOpen.startsWith('https://') || !urlToOpen.includes('://'))) {
            let finalUrl = urlToOpen;
            // Basic check if it's a relative path, might need more robust logic depending on deployment
            // if (!urlToOpen.startsWith('http://') && !urlToOpen.startsWith('https://')) {
            // finalUrl = window.location.origin + (urlToOpen.startsWith('/') ? '' : '/') + urlToOpen;
            // }
            const newWindow = window.open(finalUrl, '_blank'); 
            if (!newWindow || newWindow.closed || typeof newWindow.closed == 'undefined') {
                showReportFeedbackMessage('无法打开新窗口。请检查您的弹出窗口拦截设置。', 'error', 5000);
            }
        } else {
            showReportFeedbackMessage('昨日日报的URL未配置或无效。请检查脚本中的 previousReportUrl 变量，并确保它是一个有效的网址或相对路径。', 'error', 5000);
        }
    });

    function adjustActionButtonsLayout() {
        const actionButtonsContainer = document.querySelector('.action-buttons-container');
        if (actionButtonsContainer) {
            if (window.innerWidth <= 768) { 
                actionButtonsContainer.style.display = 'flex';
                actionButtonsContainer.style.flexWrap = 'wrap';
                actionButtonsContainer.style.justifyContent = 'center';
            } else { 
                actionButtonsContainer.style.display = 'block'; 
                actionButtonsContainer.style.flexWrap = 'nowrap'; 
            }
        }
    }
    
    // --- Event Listeners Call on Load ---
    window.addEventListener('load', () => {
        initializeMainDiagram(); // Initialize Mermaid diagram
        adjustActionButtonsLayout(); // Adjust report buttons layout
    });
    window.addEventListener('resize', adjustActionButtonsLayout); // Adjust on resize as well

    // --- Report Page JavaScript END ---
</script>
</body>
</html>

    