<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI深度研究×Gamma PPT制作 - 完整指南</title>
    
    <!-- 外部资源 -->
    <link href="https://cdn.staticfile.org/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.staticfile.org/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400;500;600;700&family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/mermaid@latest/dist/mermaid.min.js"></script>
    
    <style>
        :root {
            /* 语义化设计系统 */
            --bg-primary: light-dark(#f5f5f7, #000000);
            --bg-secondary: light-dark(#ffffff, #1d1d1f);
            --text-primary: light-dark(#1d1d1f, #f5f5f7);
            --text-secondary: light-dark(#86868b, #a1a1a6);
            --glass: light-dark(rgba(255,255,255,0.72), rgba(30,30,30,0.72));
            --accent-blue: #007AFF;
            --accent-purple: #AF52DE;
            --accent-green: #30D158;
            --accent-orange: #FF9500;
            
            /* 字体系统 */
            --font-system: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans SC", sans-serif;
            --font-serif: "Noto Serif SC", serif;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-system);
            background: var(--bg-primary);
            color: var(--text-primary);
            line-height: 1.6;
            overflow-x: hidden;
        }

        /* 第一幕：沉浸式英雄区 */
        .hero-section {
            height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .hero-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle at 30% 40%, rgba(0, 122, 255, 0.1) 0%, transparent 50%),
                        radial-gradient(circle at 70% 60%, rgba(175, 82, 222, 0.1) 0%, transparent 50%);
            filter: blur(60px);
            z-index: -1;
        }

        .hero-title {
            font-size: clamp(3rem, 8vw, 6rem);
            font-weight: 700;
            background: linear-gradient(135deg, var(--accent-blue), var(--accent-purple));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 1.5rem;
            line-height: 1.1;
        }

        .hero-subtitle {
            font-size: clamp(1.25rem, 3vw, 2rem);
            color: var(--text-secondary);
            max-width: 800px;
            margin-bottom: 3rem;
            font-weight: 300;
        }

        .hero-stats {
            display: flex;
            gap: 4rem;
            margin-top: 2rem;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 3rem;
            font-weight: 700;
            background: linear-gradient(135deg, var(--accent-green), var(--accent-blue));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            display: block;
        }

        .stat-label {
            color: var(--text-secondary);
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        /* 第二幕：数字杂志内容区 */
        .magazine-section {
            padding: 8rem 2rem;
            max-width: 1400px;
            margin: 0 auto;
        }

        .chapter {
            margin-bottom: 8rem;
            opacity: 0;
            transform: translateY(40px);
            transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .chapter.animate-in {
            opacity: 1;
            transform: translateY(0);
        }

        .chapter-header {
            text-align: center;
            margin-bottom: 4rem;
        }

        .chapter-number {
            font-size: 1rem;
            color: var(--accent-blue);
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 2px;
            margin-bottom: 1rem;
        }

        .chapter-title {
            font-size: clamp(2.5rem, 5vw, 4rem);
            font-weight: 700;
            margin-bottom: 1.5rem;
            background: linear-gradient(135deg, var(--text-primary), var(--accent-purple));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .chapter-subtitle {
            font-size: 1.25rem;
            color: var(--text-secondary);
            max-width: 600px;
            margin: 0 auto;
        }

        /* 编辑布局 */
        .editorial-layout {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4rem;
            margin-top: 4rem;
        }

        .content-card {
            background: var(--glass);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 3rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .content-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
        }

        .card-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, var(--accent-blue), var(--accent-purple));
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            margin-bottom: 1.5rem;
        }

        .card-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .card-description {
            color: var(--text-secondary);
            line-height: 1.7;
        }

        /* 特写布局 */
        .feature-layout {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 4rem;
        }

        .feature-card {
            background: var(--glass);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
        }

        .feature-number {
            font-size: 2rem;
            font-weight: 700;
            color: var(--accent-blue);
            margin-bottom: 1rem;
        }

        .feature-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 0.75rem;
        }

        .feature-description {
            color: var(--text-secondary);
            font-size: 0.95rem;
            line-height: 1.6;
        }

        /* 展示型单栏布局 */
        .showcase-layout {
            margin-top: 4rem;
        }

        .showcase-card {
            background: var(--glass);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 4rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
            text-align: center;
        }

        .showcase-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 2rem;
            background: linear-gradient(135deg, var(--accent-green), var(--accent-blue));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 3rem;
            margin-top: 3rem;
        }

        .comparison-item {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 16px;
            padding: 2rem;
        }

        .comparison-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .comparison-time {
            font-size: 2rem;
            font-weight: 700;
            color: var(--accent-orange);
            margin-bottom: 0.5rem;
        }

        /* 数据可视化 */
        .visualization-container {
            margin: 4rem 0;
            background: var(--glass);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 3rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .mermaid {
            background: transparent !important;
        }

        /* 第三幕：Bento Grid总结 */
        .finale-section {
            height: 100vh;
            padding: 2rem;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, rgba(0, 122, 255, 0.05), rgba(175, 82, 222, 0.05));
        }

        .bento-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            grid-template-rows: repeat(3, 1fr);
            gap: 1rem;
            width: 100%;
            max-width: 1200px;
            height: 80vh;
        }

        .bento-card {
            background: var(--glass);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .bento-card:hover {
            transform: scale(1.02);
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
        }

        .bento-hero {
            grid-column: 2 / 4;
            grid-row: 2 / 3;
            background: linear-gradient(135deg, var(--accent-blue), var(--accent-purple));
            color: white;
        }

        .bento-large {
            grid-column: span 2;
        }

        .bento-tall {
            grid-row: span 2;
        }

        .bento-icon {
            font-size: 2rem;
            margin-bottom: 1rem;
            opacity: 0.8;
        }

        .bento-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 0.75rem;
        }

        .bento-value {
            font-size: 2.5rem;
            font-weight: 700;
            background: linear-gradient(135deg, var(--accent-green), var(--accent-blue));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 0.5rem;
        }

        .bento-description {
            font-size: 0.9rem;
            opacity: 0.8;
            line-height: 1.4;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .hero-stats {
                flex-direction: column;
                gap: 2rem;
            }

            .editorial-layout {
                grid-template-columns: 1fr;
                gap: 2rem;
            }

            .feature-layout {
                grid-template-columns: 1fr;
            }

            .comparison-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }

            .bento-grid {
                grid-template-columns: repeat(2, 1fr);
                grid-template-rows: repeat(6, 1fr);
                height: 90vh;
            }

            .bento-hero {
                grid-column: 1 / 3;
                grid-row: 3 / 4;
            }

            .bento-large {
                grid-column: span 1;
            }

            .bento-tall {
                grid-row: span 1;
            }
        }

        /* 动画定义 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animate-delay-100 { animation-delay: 0.1s; }
        .animate-delay-200 { animation-delay: 0.2s; }
        .animate-delay-300 { animation-delay: 0.3s; }
        .animate-delay-400 { animation-delay: 0.4s; }
    </style>
</head>
<body>
    <!-- 第一幕：沉浸式英雄区 -->
    <section class="hero-section">
        <div class="hero-bg"></div>
        <div class="hero-content">
            <h1 class="hero-title">AI×深度研究<br/>智能PPT制作</h1>
            <p class="hero-subtitle">融合Gemini Deep Research与ChatGPT深度分析，配合Gamma AI快速生成专业演示文稿，效率提升40倍的完整工作流程</p>
            
            <div class="hero-stats">
                <div class="stat-item">
                    <span class="stat-number">20</span>
                    <span class="stat-label">分钟完成</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">40×</span>
                    <span class="stat-label">效率提升</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">99%</span>
                    <span class="stat-label">准确率</span>
                </div>
            </div>
        </div>
    </section>

    <!-- 第二幕：数字杂志内容区 -->
    <main class="magazine-section">
        <!-- 章节1：研究方案选择 -->
        <article class="chapter">
            <header class="chapter-header">
                <div class="chapter-number">第一步</div>
                <h2 class="chapter-title">选择最适合的研究方案</h2>
                <p class="chapter-subtitle">理解两大AI研究工具的核心差异，为不同场景选择最优方案</p>
            </header>
            
            <div class="editorial-layout">
                <div class="content-card">
                    <div class="card-icon">
                        <i class="fab fa-google"></i>
                    </div>
                    <h3 class="card-title">Gemini Deep Research</h3>
                    <p class="card-description">
                        广度优先的全景分析，采用Gemini 2.5 Pro驱动。自动生成宽泛研究方案，覆盖市场背景、玩家特征、厂商情况、技术趋势等多维度。
                        <br/><br/>
                        <strong>适用场景：</strong>初次接触行业，需要建立完整知识框架的全面了解。
                    </p>
                </div>
                
                <div class="content-card">
                    <div class="card-icon">
                        <i class="fas fa-robot"></i>
                    </div>
                    <h3 class="card-title">ChatGPT Deep Research</h3>
                    <p class="card-description">
                        精度优先的深度挖掘，通过交互式问题细化确保研究方向精准。能够准确识别具体产品信息，包括开发商、测试时间、核心玩法等详细数据。
                        <br/><br/>
                        <strong>适用场景：</strong>有明确研究目标，需要精准数据支撑决策的深度分析。
                    </p>
                </div>
            </div>
            
            <div class="showcase-layout">
                <div class="showcase-card">
                    <h3 class="showcase-title">技术优势对比</h3>
                    <div class="comparison-grid">
                        <div class="comparison-item">
                            <div class="comparison-title">
                                <i class="fab fa-google" style="color: var(--accent-blue);"></i>
                                Gemini优势
                            </div>
                            <div class="comparison-time">广度+深度</div>
                            <p>自动浏览数百网站，交叉验证信息，提供行业全景分析和背景知识深度讲解</p>
                        </div>
                        <div class="comparison-item">
                            <div class="comparison-title">
                                <i class="fas fa-robot" style="color: var(--accent-purple);"></i>
                                GPT优势
                            </div>
                            <div class="comparison-time">精度+准确</div>
                            <p>交互式问题细化，精准识别具体产品信息，数据来源明确，无AI幻觉问题</p>
                        </div>
                    </div>
                </div>
            </div>
        </article>

        <!-- 数据可视化：工作流程图 -->
        <div class="visualization-container">
            <h3 style="text-align: center; margin-bottom: 2rem; font-size: 1.5rem; font-weight: 600;">AI研究工作流程</h3>
            <div class="mermaid">
graph TD
    A[确定研究目标] --> B{选择研究工具}
    B --> C[Gemini Deep Research<br/>广度优先]
    B --> D[ChatGPT Deep Research<br/>精度优先]
    C --> E[自动生成研究方案]
    D --> F[交互式问题细化]
    E --> G[多维度全景分析]
    F --> H[精准深度挖掘]
    G --> I[Gamma PPT制作]
    H --> I
    I --> J[20分钟完成专业PPT]
    
    style A fill:#007AFF,stroke:#fff,stroke-width:2px,color:#fff
    style J fill:#30D158,stroke:#fff,stroke-width:2px,color:#fff
    style C fill:#AF52DE,stroke:#fff,stroke-width:2px,color:#fff
    style D fill:#FF9500,stroke:#fff,stroke-width:2px,color:#fff
            </div>
        </div>

        <!-- 章节2：研究结果对比 -->
        <article class="chapter">
            <header class="chapter-header">
                <div class="chapter-number">第二步</div>
                <h2 class="chapter-title">研究结果对比与质量评估</h2>
                <p class="chapter-subtitle">深度分析两种工具的输出差异，掌握质量评估标准</p>
            </header>
            
            <div class="feature-layout">
                <div class="feature-card">
                    <div class="feature-number">01</div>
                    <h4 class="feature-title">GPT精确特点</h4>
                    <p class="feature-description">准确找到Dark War、ThroneFall、KingShot等新产品详细信息，包括测试时间、上线时间、关键KPI数据等，信息源明确可追溯。</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-number">02</div>
                    <h4 class="feature-title">Gemini宽广特点</h4>
                    <p class="feature-description">深入讲解4X策略游戏历史演进、核心玩法机制发展、不同地区玩家文化偏好差异，提供完整行业背景知识。</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-number">03</div>
                    <h4 class="feature-title">多源验证机制</h4>
                    <p class="feature-description">Deep Research采用边思考边搜索的迭代方法，自动识别信息相关性和权威性，过滤低质量内容，确保数据完全可信。</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-number">04</div>
                    <h4 class="feature-title">推理能力强化</h4>
                    <p class="feature-description">从分散信息中提取关键洞察，建立不同数据点关联关系，使用最新AI推理能力提升搜索质量。</p>
                </div>
            </div>
        </article>

        <!-- 章节3：Gamma制作流程 -->
        <article class="chapter">
            <header class="chapter-header">
                <div class="chapter-number">第三步</div>
                <h2 class="chapter-title">Gamma制作PPT完整流程</h2>
                <p class="chapter-subtitle">掌握2025年最领先AI演示文稿制作工具的专业技巧</p>
            </header>
            
            <div class="feature-layout">
                <div class="feature-card">
                    <div class="feature-number">01</div>
                    <h4 class="feature-title">导入内容</h4>
                    <p class="feature-description">选择"粘贴文本"，复制完整研究报告。Gamma自动分析内容结构，识别关键信息点。</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-number">02</div>
                    <h4 class="feature-title">内容处理技巧</h4>
                    <p class="feature-description">先选择"保留原文"让系统自动切割分页，完成后切换"压缩模式"，确保内容结构完整且页面长度合适。</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-number">03</div>
                    <h4 class="feature-title">图像生成优化</h4>
                    <p class="feature-description">选择GPT Image模型，指令遵循能力最强，生成图片高度贴合PPT内容，理解商业场景和专业概念。</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-number">04</div>
                    <h4 class="feature-title">模板风格选择</h4>
                    <p class="feature-description">商业分析、科技创新、投资报告、教育培训等专业模板，自动版式设计，确保品牌一致性。</p>
                </div>
            </div>
            
            <div class="showcase-layout">
                <div class="showcase-card">
                    <h3 class="showcase-title">自动化生成优势</h3>
                    <div class="comparison-grid">
                        <div class="comparison-item">
                            <div class="comparison-title">
                                <i class="fas fa-compress-alt" style="color: var(--accent-green);"></i>
                                智能内容压缩
                            </div>
                            <p>自动识别核心信息，去除冗余内容，确保每页PPT信息密度适中，突出关键价值。</p>
                        </div>
                        <div class="comparison-item">
                            <div class="comparison-title">
                                <i class="fas fa-palette" style="color: var(--accent-orange);"></i>
                                自动版式设计
                            </div>
                            <p>根据内容类型选择最合适版式，文字、图表、图片布局完全自动化，保持专业设计水准。</p>
                        </div>
                    </div>
                </div>
            </div>
        </article>

        <!-- 章节4：效率分析与应用 -->
        <article class="chapter">
            <header class="chapter-header">
                <div class="chapter-number">总结展望</div>
                <h2 class="chapter-title">效率革命与应用前景</h2>
                <p class="chapter-subtitle">深度分析这套方法论的价值意义和未来发展方向</p>
            </header>
            
            <div class="showcase-layout">
                <div class="showcase-card">
                    <h3 class="showcase-title">成本效益对比</h3>
                    <div class="comparison-grid">
                        <div class="comparison-item">
                            <div class="comparison-title">
                                <i class="fas fa-clock" style="color: var(--accent-blue);"></i>
                                传统方法
                            </div>
                            <div class="comparison-time">18-28小时</div>
                            <p>信息收集8-12小时 + 资料整理4-6小时 + PPT制作6-10小时</p>
                        </div>
                        <div class="comparison-item">
                            <div class="comparison-title">
                                <i class="fas fa-rocket" style="color: var(--accent-green);"></i>
                                AI辅助方法
                            </div>
                            <div class="comparison-time">15-28分钟</div>
                            <p>Deep Research 5-10分钟 + 结果整理5-10分钟 + Gamma生成5-8分钟</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="editorial-layout">
                <div class="content-card">
                    <div class="card-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h3 class="card-title">质量提升维度</h3>
                    <p class="card-description">
                        <strong>信息全面性：</strong>AI覆盖更广泛信息源<br/>
                        <strong>数据准确性：</strong>多源验证减少错误<br/>
                        <strong>设计专业性：</strong>AI模板保证视觉质量<br/>
                        <strong>一致性保证：</strong>避免人工操作不一致
                    </p>
                </div>
                
                <div class="content-card">
                    <div class="card-icon">
                        <i class="fas fa-globe"></i>
                    </div>
                    <h3 class="card-title">广泛应用场景</h3>
                    <p class="card-description">
                        <strong>消费品市场研究</strong><br/>
                        <strong>技术趋势分析</strong><br/>
                        <strong>投资机会评估</strong><br/>
                        <strong>竞争对手调研</strong><br/>
                        <strong>产品发布策略</strong>
                    </p>
                </div>
            </div>
        </article>
    </main>

    <!-- 第三幕：Bento Grid总结 -->
    <section class="finale-section">
        <div class="bento-grid">
            <!-- 第一行 -->
            <div class="bento-card">
                <div class="bento-icon">⚡</div>
                <div class="bento-title">效率提升</div>
                <div class="bento-value">40×</div>
                <div class="bento-description">传统方法到AI辅助的效率跃升</div>
            </div>
            
            <div class="bento-card bento-hero">
                <div style="font-size: 3rem; margin-bottom: 1rem;">🚀</div>
                <h3 style="font-size: 2rem; font-weight: 700; margin-bottom: 1rem;">AI×研究×设计</h3>
                <p style="font-size: 1.1rem; opacity: 0.9;">重新定义知识工作的<br/>完整解决方案</p>
            </div>
            
            <div class="bento-card">
                <div class="bento-icon">🎯</div>
                <div class="bento-title">准确率</div>
                <div class="bento-value">99%</div>
                <div class="bento-description">多源验证确保数据可信度</div>
            </div>
            
            <!-- 第二行中央为Hero卡片 -->
            
            <!-- 第三行 -->
            <div class="bento-card">
                <div class="bento-icon">📊</div>
                <div class="bento-title">研究深度</div>
                <div class="bento-value">数百</div>
                <div class="bento-description">网站信息源自动筛选分析</div>
            </div>
            
            <div class="bento-card">
                <div class="bento-icon">🎨</div>
                <div class="bento-title">设计质量</div>
                <div class="bento-value">专业</div>
                <div class="bento-description">AI模板确保视觉一致性</div>
            </div>
            
            <div class="bento-card">
                <div class="bento-icon">⏱️</div>
                <div class="bento-title">完成时间</div>
                <div class="bento-value">20</div>
                <div class="bento-description">分钟完成完整工作流程</div>
            </div>
            
            <div class="bento-card">
                <div class="bento-icon">🌐</div>
                <div class="bento-title">应用领域</div>
                <div class="bento-value">全覆盖</div>
                <div class="bento-description">市场研究到产品策略</div>
            </div>
        </div>
    </section>

    <script>
        // Mermaid初始化
        mermaid.initialize({ 
            theme: 'dark',
            themeVariables: {
                primaryColor: '#007AFF',
                primaryTextColor: '#f5f5f7',
                primaryBorderColor: '#AF52DE',
                lineColor: '#86868b',
                secondaryColor: '#30D158',
                tertiaryColor: '#FF9500'
            }
        });

        // Intersection Observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -100px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach((entry, index) => {
                if (entry.isIntersecting) {
                    setTimeout(() => {
                        entry.target.classList.add('animate-in');
                    }, index * 200);
                }
            });
        }, observerOptions);

        // 观察所有chapter元素
        document.querySelectorAll('.chapter').forEach(chapter => {
            observer.observe(chapter);
        });

        // Bento Grid动画
        const bentoObserver = new IntersectionObserver((entries) => {
            entries.forEach((entry) => {
                if (entry.isIntersecting) {
                    const cards = entry.target.querySelectorAll('.bento-card');
                    cards.forEach((card, index) => {
                        setTimeout(() => {
                            card.style.animation = `fadeInUp 0.6s ease forwards`;
                        }, index * 100);
                    });
                }
            });
        }, { threshold: 0.2 });

        document.querySelectorAll('.bento-grid').forEach(grid => {
            bentoObserver.observe(grid);
        });

        // 平滑滚动
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
</body>
</html> 