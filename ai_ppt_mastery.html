<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI PPT 制作大师班</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #1d1d1f;
            overflow-x: hidden;
        }

        /* 全局动画系统 */
        .fade-in {
            opacity: 0;
            transform: translateY(40px);
            transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .fade-in.visible {
            opacity: 1;
            transform: translateY(0);
        }

        /* 第一幕：英雄开场 */
        .hero-section {
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            background: linear-gradient(135deg, #f5f5f7 0%, #ffffff 100%);
            overflow: hidden;
        }

        .hero-content {
            text-align: center;
            max-width: 1000px;
            padding: 0 40px;
            z-index: 2;
        }

        .hero-title {
            font-size: 96px;
            font-weight: 700;
            line-height: 1.05;
            letter-spacing: -0.02em;
            margin-bottom: 24px;
            background: linear-gradient(135deg, #1d1d1f 0%, #86868b 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .hero-subtitle {
            font-size: 32px;
            font-weight: 500;
            color: #86868b;
            margin-bottom: 48px;
        }

        .hero-description {
            font-size: 24px;
            color: #515154;
            max-width: 800px;
            margin: 0 auto;
        }

        /* 装饰球体 */
        .gradient-orb {
            position: absolute;
            border-radius: 50%;
            filter: blur(100px);
            opacity: 0.3;
            pointer-events: none;
        }

        .orb-blue {
            width: 600px;
            height: 600px;
            background: radial-gradient(circle, #007AFF, transparent);
            top: -200px;
            right: -200px;
        }

        .orb-green {
            width: 400px;
            height: 400px;
            background: radial-gradient(circle, #30D158, transparent);
        }

        /* 第二幕：章节设计 */
        .chapter {
            margin-bottom: 120px;
        }

        .chapter-hero {
            height: 60vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #000000;
            color: #ffffff;
            position: relative;
            overflow: hidden;
        }

        .chapter-number {
            font-size: 200px;
            font-weight: 800;
            opacity: 0.1;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .chapter-title {
            font-size: 72px;
            font-weight: 700;
            z-index: 1;
            position: relative;
            text-align: center;
        }

        /* 章节内容区 - Bento Grid */
        .chapter-content {
            padding: 120px 60px;
            max-width: 1440px;
            margin: 0 auto;
        }

        .bento-grid {
            display: grid;
            grid-template-columns: repeat(12, 1fr);
            gap: 24px;
            margin-bottom: 120px;
        }

        /* 卡片基础样式 */
        .card {
            background: #ffffff;
            border-radius: 24px;
            padding: 48px;
            box-shadow: 0 4px 24px rgba(0, 0, 0, 0.08);
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-8px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
        }

        /* 卡片尺寸变体 */
        .card-large { grid-column: span 8; min-height: 400px; }
        .card-medium { grid-column: span 6; min-height: 300px; }
        .card-small { grid-column: span 4; min-height: 240px; }
        .card-wide { grid-column: span 12; min-height: 200px; }

        /* 特殊布局组合 */
        .feature-group {
            display: contents;
        }

        .feature-group-1 .main-card { grid-column: 1 / 8; grid-row: span 2; }
        .feature-group-1 .sub-card-1 { grid-column: 8 / 13; }
        .feature-group-1 .sub-card-2 { grid-column: 8 / 13; }

        .feature-group-2 .wide-card { grid-column: 1 / 13; }
        .feature-group-2 .split-card-1 { grid-column: 1 / 5; }
        .feature-group-2 .split-card-2 { grid-column: 5 / 9; }
        .feature-group-2 .split-card-3 { grid-column: 9 / 13; }

        /* 数据展示卡片 */
        .data-card {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            text-align: center;
        }

        .data-value {
            font-size: 84px;
            font-weight: 700;
            line-height: 1;
            margin: 24px 0;
            background: linear-gradient(135deg, #007AFF 0%, #30D158 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .data-unit {
            font-size: 32px;
            font-weight: 500;
            color: #86868b;
        }

        .data-label {
            font-size: 18px;
            color: #86868b;
            margin-bottom: 16px;
        }

        /* 特性卡片 */
        .feature-card {
            background: linear-gradient(135deg, #ffffff 0%, #f5f5f7 100%);
        }

        .feature-icon {
            font-size: 48px;
            margin-bottom: 24px;
            opacity: 0.3;
        }

        .feature-title {
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 16px;
        }

        .feature-description {
            font-size: 18px;
            color: #86868b;
            line-height: 1.5;
        }

        /* 对比卡片 */
        .vs-card {
            background: linear-gradient(135deg, #000000 0%, #1d1d1f 100%);
            color: #ffffff;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .vs-content {
            text-align: center;
        }

        .vs-title {
            font-size: 48px;
            font-weight: 700;
            margin-bottom: 16px;
        }

        .vs-subtitle {
            font-size: 20px;
            color: #86868b;
        }

        /* 引言卡片 */
        .quote-card {
            display: flex;
            align-items: center;
            font-size: 28px;
            font-weight: 500;
            line-height: 1.4;
            font-style: italic;
            color: #1d1d1f;
        }

        /* 第三幕：中心辐射总结 */
        .finale-section {
            min-height: 100vh;
            padding: 80px 40px;
            background: radial-gradient(ellipse at center, #f5f5f7 0%, #e8e8ed 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .universe-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            grid-template-rows: repeat(5, minmax(140px, 1fr));
            gap: 20px;
            width: 100%;
            max-width: 1600px;
            height: 90vh;
            position: relative;
        }

        /* 中心主卡片 */
        .finale-hero {
            grid-column: 3 / 6;
            grid-row: 2 / 4;
            z-index: 10;
            transform: scale(1.05);
            background: linear-gradient(135deg, #007AFF 0%, #30D158 100%);
            color: #ffffff;
            display: flex;
            flex-direction: column;
            justify-content: center;
            text-align: center;
        }

        .finale-title {
            font-size: 48px;
            font-weight: 700;
            margin-bottom: 16px;
        }

        .finale-subtitle {
            font-size: 24px;
            opacity: 0.9;
        }

        .finale-description {
            font-size: 18px;
            opacity: 0.8;
            margin-top: 8px;
        }

        /* 环绕卡片定位 */
        .orbit-card-1 { grid-column: 1 / 3; grid-row: 1 / 2; }
        .orbit-card-2 { grid-column: 5 / 7; grid-row: 1 / 2; }
        .orbit-card-3 { grid-column: 1 / 3; grid-row: 3 / 4; }
        .orbit-card-4 { grid-column: 6 / 8; grid-row: 2 / 4; }
        .orbit-card-5 { grid-column: 2 / 4; grid-row: 4 / 5; }
        .orbit-card-6 { grid-column: 5 / 7; grid-row: 4 / 5; }

        /* 步骤卡片样式 */
        .step-card {
            background: linear-gradient(135deg, #ffffff 0%, #f9f9fb 100%);
            padding: 32px;
        }

        .step-header {
            font-size: 14px;
            color: #86868b;
            font-weight: 500;
            margin-bottom: 8px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .step-title {
            font-size: 20px;
            font-weight: 600;
            color: #1d1d1f;
            margin-bottom: 16px;
        }

        .step-content p {
            font-size: 14px;
            color: #515154;
            margin-bottom: 8px;
            line-height: 1.4;
        }

        .step-content p:last-child {
            margin-bottom: 0;
        }

        .step-note {
            color: #86868b !important;
            font-style: italic;
        }

        /* 时间卡片样式 */
        .time-card {
            background: linear-gradient(135deg, #f5f5f7 0%, #ffffff 100%);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            padding: 32px;
        }

        .time-label {
            font-size: 14px;
            color: #86868b;
            margin-bottom: 8px;
        }

        .time-value {
            font-size: 56px;
            font-weight: 700;
            line-height: 1;
            margin: 8px 0;
            background: linear-gradient(135deg, #007AFF 0%, #30D158 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .time-unit {
            font-size: 18px;
            color: #515154;
            font-weight: 500;
        }

        /* 亮点卡片样式 */
        .highlight-card {
            background: linear-gradient(135deg, #007AFF 0%, #30D158 100%);
            color: #ffffff;
            padding: 32px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .highlight-title {
            font-size: 22px;
            font-weight: 600;
            margin-bottom: 12px;
        }

        .highlight-description {
            font-size: 14px;
            line-height: 1.4;
            opacity: 0.9;
        }

        /* 总结卡片样式 */
        .summary-card {
            background: linear-gradient(135deg, #1d1d1f 0%, #000000 100%);
            color: #ffffff;
            padding: 32px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .summary-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 12px;
        }

        .summary-description {
            font-size: 14px;
            line-height: 1.4;
            color: #d1d1d6;
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .hero-title { font-size: 72px; }
            .chapter-title { font-size: 56px; }
            .bento-grid { grid-template-columns: repeat(8, 1fr); }
            .feature-group-1 .main-card { grid-column: 1 / 6; }
            .feature-group-1 .sub-card-1 { grid-column: 6 / 9; }
            .feature-group-1 .sub-card-2 { grid-column: 6 / 9; }
        }

        @media (max-width: 768px) {
            .hero-title { font-size: 48px; }
            .chapter-title { font-size: 40px; }
            .bento-grid { grid-template-columns: repeat(4, 1fr); }
            .card-large, .card-medium, .card-small, .card-wide { 
                grid-column: 1 / -1; 
            }
            .universe-grid {
                grid-template-columns: repeat(3, 1fr);
                grid-template-rows: repeat(7, minmax(120px, 1fr));
            }
            .finale-hero { grid-column: 1 / 4; grid-row: 3 / 5; }
        }
    </style>
</head>
<body>
    <div class="page-container">
        <!-- 第一幕：英雄开场 -->
        <section class="hero-section">
            <div class="hero-content fade-in">
                <h1 class="hero-title">AI PPT 制作大师班</h1>
                <p class="hero-subtitle">DeepResearch + Gamma = 专业级演示文稿</p>
                <p class="hero-description">从研究到成稿，20分钟完成专业行业分析PPT</p>
            </div>
            <div class="gradient-orb orb-blue"></div>
        </section>

        <!-- 第二幕：分章节展开 -->
        <!-- 章节1：选择研究方案 -->
        <section class="chapter">
            <div class="chapter-hero">
                <span class="chapter-number">01</span>
                <h2 class="chapter-title">选择研究方案</h2>
            </div>
            <div class="chapter-content">
                <div class="bento-grid">
                    <div class="feature-group feature-group-1">
                        <div class="card main-card feature-card fade-in">
                            <div class="feature-icon">🤖</div>
                            <h3 class="feature-title">AI 研究新时代</h3>
                            <p class="feature-description">Gemini Deep Research 与 GPT Deep Research 各具优势，为我们提供了前所未有的研究能力。选择合适的工具，让AI成为你的研究助手。</p>
                        </div>
                        <div class="card sub-card-1 data-card fade-in">
                            <p class="data-label">Gemini 特色</p>
                            <p class="data-value">广</p>
                            <p class="data-unit">覆盖面广，全面概览</p>
                        </div>
                        <div class="card sub-card-2 data-card fade-in">
                            <p class="data-label">GPT 特色</p>
                            <p class="data-value">深</p>
                            <p class="data-unit">精确深挖，深度分析</p>
                        </div>
                    </div>
                </div>
                
                <div class="bento-grid">
                    <div class="feature-group feature-group-2">
                        <div class="card wide-card vs-card fade-in">
                            <div class="vs-content">
                                <h3 class="vs-title">Gemini vs GPT</h3>
                                <p class="vs-subtitle">两种思路，一个目标：高质量研究报告</p>
                            </div>
                        </div>
                        <div class="card split-card-1 feature-card fade-in">
                            <h4 class="feature-title">Gemini 优势</h4>
                            <p class="feature-description">自动生成宽泛研究方案，从市场背景到玩家特征，提供完整行业图景</p>
                        </div>
                        <div class="card split-card-2 feature-card fade-in">
                            <h4 class="feature-title">GPT 优势</h4>
                            <p class="feature-description">持续细化问题导向，目标平台、市场、厂商逐步精确，适合深度挖掘</p>
                        </div>
                        <div class="card split-card-3 quote-card fade-in">
                            "开多个窗口，让它持续细化问题，直到问题足够精确为止"
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 章节2：研究结果对比 -->
        <section class="chapter">
            <div class="chapter-hero">
                <span class="chapter-number">02</span>
                <h2 class="chapter-title">研究结果对比</h2>
            </div>
            <div class="chapter-content">
                <div class="bento-grid">
                    <div class="feature-group feature-group-1">
                        <div class="card main-card feature-card fade-in">
                            <div class="feature-icon">🎯</div>
                            <h3 class="feature-title">精准 vs 全面</h3>
                            <p class="feature-description">GPT精确定位Dark War、ThroneFall、KingShot等新产品，包含详细的测试和上线时间。Gemini则提供更广泛的背景知识和玩家特征分析。</p>
                        </div>
                        <div class="card sub-card-1 data-card fade-in">
                            <p class="data-label">数据可信度</p>
                            <p class="data-value">100%</p>
                            <p class="data-unit">边思考边搜索，无AI幻觉</p>
                        </div>
                        <div class="card sub-card-2 feature-card fade-in">
                            <h4 class="feature-title">质量保证</h4>
                            <p class="feature-description">Deep Research采用边思考边搜索新内容的方式，确保搜索质量和数据完全可信</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 章节3：Gamma制作PPT -->
        <section class="chapter">
            <div class="chapter-hero">
                <span class="chapter-number">03</span>
                <h2 class="chapter-title">Gamma制作PPT</h2>
            </div>
            <div class="chapter-content">
                <div class="bento-grid">
                    <div class="feature-group feature-group-2">
                        <div class="card wide-card feature-card fade-in">
                            <h3 class="feature-title">关键技巧揭秘</h3>
                            <p class="feature-description">先选择"保留原文"模式让Gamma自动切割分页，完成后再切换回"压缩模式"，这样就能保证自动分好页了</p>
                        </div>
                        <div class="card split-card-1 data-card fade-in">
                            <p class="data-label">生成时间</p>
                            <p class="data-value">20</p>
                            <p class="data-unit">分钟完整流程</p>
                        </div>
                        <div class="card split-card-2 feature-card fade-in">
                            <h4 class="feature-title">图片生成</h4>
                            <p class="feature-description">选择GPT Image模型，指令遵循能力最强，生成图片高度贴合PPT内容</p>
                        </div>
                        <div class="card split-card-3 feature-card fade-in">
                            <h4 class="feature-title">自动化流程</h4>
                            <p class="feature-description">自动压缩内容、排版设计、生成相关图片，完全自动化制作专业PPT</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 第三幕：中心辐射总结 -->
        <section class="finale-section">
            <div class="gradient-orb orb-green" style="top: 10%; left: 20%;"></div>
            <div class="universe-grid">
                <div class="card finale-hero fade-in">
                    <h2 class="finale-title">专业PPT制作</h2>
                    <p class="finale-subtitle">DeepResearch + Gamma</p>
                    <p class="finale-description">快速制作专业演示文稿</p>
                </div>
                
                <div class="card orbit-card-1 step-card fade-in">
                    <div class="step-header">第一步</div>
                    <h4 class="step-title">选择研究方案</h4>
                    <div class="step-content">
                        <p><strong>Gemini</strong>覆盖面广</p>
                        <p><strong>GPT</strong>精确深挖</p>
                    </div>
                </div>
                
                <div class="card orbit-card-2 time-card fade-in">
                    <p class="time-label">完成时间</p>
                    <p class="time-value">20</p>
                    <p class="time-unit">分钟</p>
                </div>
                
                <div class="card orbit-card-3 highlight-card fade-in">
                    <h4 class="highlight-title">Deep Research</h4>
                    <p class="highlight-description">边思考边搜索，数据完全可信，无AI幻觉</p>
                </div>
                
                <div class="card orbit-card-4 step-card fade-in">
                    <div class="step-header">第二步</div>
                    <h4 class="step-title">研究结果对比</h4>
                    <div class="step-content">
                        <p><strong>GPT</strong>报告精确，准确找到新产品及时间</p>
                        <p><strong>Gemini</strong>报告宽泛，覆盖背景知识玩家特征</p>
                        <p class="step-note">两种方案互相补充</p>
                    </div>
                </div>
                
                <div class="card orbit-card-5 step-card fade-in">
                    <div class="step-header">第三步</div>
                    <h4 class="step-title">Gamma制作PPT</h4>
                    <div class="step-content">
                        <p><strong>关键技巧：</strong>先选择"保留原文"模式自动分割分页，再切换"压缩模式"</p>
                        <p><strong>选择GPT Image模型</strong>生成高质量图片</p>
                        <p>自动压缩内容、排版、生成相关图片</p>
                    </div>
                </div>
                
                <div class="card orbit-card-6 summary-card fade-in">
                    <h4 class="summary-title">AI工作流程</h4>
                    <p class="summary-description">通过Deep Research增强输入信息，再用Gamma快速生成结构化强、图片质量高的PPT</p>
                </div>
            </div>
        </section>
    </div>

    <script>
        // 滚动触发动画
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                }
            });
        }, { 
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        });

        // 观察所有需要动画的元素
        document.querySelectorAll('.fade-in').forEach(el => {
            observer.observe(el);
        });

        // 简单的视差滚动效果
        window.addEventListener('scroll', () => {
            const scrollY = window.scrollY;
            const orbs = document.querySelectorAll('.gradient-orb');
            
            orbs.forEach((orb, index) => {
                const speed = 0.5 + (index * 0.2);
                orb.style.transform = `translate(${scrollY * speed * 0.1}px, ${scrollY * speed * 0.05}px)`;
            });
        });

        // 页面加载完成后触发首屏动画
        window.addEventListener('load', () => {
            setTimeout(() => {
                document.querySelector('.hero-content').classList.add('visible');
            }, 300);
        });
    </script>
</body>
</html> 